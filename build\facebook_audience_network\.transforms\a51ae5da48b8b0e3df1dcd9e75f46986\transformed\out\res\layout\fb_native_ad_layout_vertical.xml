<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/black">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.facebook.ads.MediaView
            android:id="@+id/native_ad_media"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/native_ad_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|top"
            android:padding="2dp"
            android:background="#44000000"
            android:orientation="horizontal">
        </LinearLayout>
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:paddingTop="5dp"
        android:paddingBottom="16dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/native_ad_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="2"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/native_ad_sponsored_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:textSize="11dp"
                        android:textStyle="normal"
                        android:textColor="#9ba1b4" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/native_ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:includeFontPadding="false"
            android:maxLines="4"
            android:textColor="@android:color/white"
            android:textSize="12dp"
            android:textStyle="normal"/>

        <LinearLayout
            android:id="@+id/native_ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_gravity="right"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:textColor="@android:color/black">

            <TextView
                android:id="@+id/native_ad_button_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#000000"
                android:textSize="12dp"
                android:textStyle="normal" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>