{"logs": [{"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,3733", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,3811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3595", "endColumns": "137", "endOffsets": "3728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "29,30,31,32,33,34,35,38", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2854,2951,3061,3163,3264,3371,3476,3816", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "2946,3056,3158,3259,3366,3471,3590,3912"}}]}, {"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-36:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,2932"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,911,1002,1094,1189,1283,1384,1477,1572,1680,1771,1862,1944,2058,2166,2266,2380,2487,2595,2755,3733", "endColumns": "119,105,104,85,109,120,79,77,90,91,94,93,100,92,94,107,90,90,81,113,107,99,113,106,107,159,98,82", "endOffsets": "220,326,431,517,627,748,828,906,997,1089,1184,1278,1379,1472,1567,1675,1766,1857,1939,2053,2161,2261,2375,2482,2590,2750,2849,3811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3595", "endColumns": "137", "endOffsets": "3728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "29,30,31,32,33,34,35,38", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2854,2951,3061,3163,3264,3371,3476,3816", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "2946,3056,3158,3259,3366,3471,3590,3912"}}]}]}