int anim fragment_fast_out_extra_slow_in 0x0
int animator fragment_close_enter 0x0
int animator fragment_close_exit 0x0
int animator fragment_fade_enter 0x0
int animator fragment_fade_exit 0x0
int animator fragment_open_enter 0x0
int animator fragment_open_exit 0x0
int array cast_expanded_controller_default_control_buttons 0x0
int array cast_mini_controller_default_control_buttons 0x0
int attr activityAction 0x0
int attr activityName 0x0
int attr adSize 0x0
int attr adSizes 0x0
int attr adUnitId 0x0
int attr allowShortcuts 0x0
int attr alpha 0x0
int attr alwaysExpand 0x0
int attr ambientEnabled 0x0
int attr animationBackgroundColor 0x0
int attr appTheme 0x0
int attr buttonSize 0x0
int attr buyButtonAppearance 0x0
int attr buyButtonHeight 0x0
int attr buyButtonText 0x0
int attr buyButtonWidth 0x0
int attr cameraBearing 0x0
int attr cameraMaxZoomPreference 0x0
int attr cameraMinZoomPreference 0x0
int attr cameraTargetLat 0x0
int attr cameraTargetLng 0x0
int attr cameraTilt 0x0
int attr cameraZoom 0x0
int attr castBackground 0x0
int attr castBackgroundColor 0x0
int attr castButtonBackgroundColor 0x0
int attr castButtonColor 0x0
int attr castButtonText 0x0
int attr castButtonTextAppearance 0x0
int attr castClosedCaptionsButtonDrawable 0x0
int attr castControlButtons 0x0
int attr castExpandedControllerStyle 0x0
int attr castExpandedControllerToolbarStyle 0x0
int attr castFocusRadius 0x0
int attr castForward30ButtonDrawable 0x0
int attr castIntroOverlayStyle 0x0
int attr castLargePauseButtonDrawable 0x0
int attr castLargePlayButtonDrawable 0x0
int attr castLargeStopButtonDrawable 0x0
int attr castMiniControllerStyle 0x0
int attr castMuteToggleButtonDrawable 0x0
int attr castPauseButtonDrawable 0x0
int attr castPlayButtonDrawable 0x0
int attr castProgressBarColor 0x0
int attr castRewind30ButtonDrawable 0x0
int attr castSeekBarProgressDrawable 0x0
int attr castSeekBarThumbDrawable 0x0
int attr castShowImageThumbnail 0x0
int attr castSkipNextButtonDrawable 0x0
int attr castSkipPreviousButtonDrawable 0x0
int attr castStopButtonDrawable 0x0
int attr castSubtitleTextAppearance 0x0
int attr castTitleTextAppearance 0x0
int attr circleCrop 0x0
int attr clearTop 0x0
int attr colorScheme 0x0
int attr contentProviderUri 0x0
int attr coordinatorLayoutStyle 0x0
int attr corpusId 0x0
int attr corpusVersion 0x0
int attr defaultIntentAction 0x0
int attr defaultIntentActivity 0x0
int attr defaultIntentData 0x0
int attr documentMaxAgeSecs 0x0
int attr environment 0x0
int attr featureType 0x0
int attr finishPrimaryWithPlaceholder 0x0
int attr finishPrimaryWithSecondary 0x0
int attr finishSecondaryWithPrimary 0x0
int attr font 0x0
int attr fontProviderAuthority 0x0
int attr fontProviderCerts 0x0
int attr fontProviderFetchStrategy 0x0
int attr fontProviderFetchTimeout 0x0
int attr fontProviderPackage 0x0
int attr fontProviderQuery 0x0
int attr fontProviderSystemFontFamily 0x0
int attr fontStyle 0x0
int attr fontVariationSettings 0x0
int attr fontWeight 0x0
int attr fragmentMode 0x0
int attr fragmentStyle 0x0
int attr imageAspectRatio 0x0
int attr imageAspectRatioAdjust 0x0
int attr indexPrefixes 0x0
int attr inputEnabled 0x0
int attr keylines 0x0
int attr lStar 0x0
int attr latLngBoundsNorthEastLatitude 0x0
int attr latLngBoundsNorthEastLongitude 0x0
int attr latLngBoundsSouthWestLatitude 0x0
int attr latLngBoundsSouthWestLongitude 0x0
int attr layout_anchor 0x0
int attr layout_anchorGravity 0x0
int attr layout_behavior 0x0
int attr layout_dodgeInsetEdges 0x0
int attr layout_insetEdge 0x0
int attr layout_keyline 0x0
int attr liteMode 0x0
int attr mapType 0x0
int attr maskedWalletDetailsBackground 0x0
int attr maskedWalletDetailsButtonBackground 0x0
int attr maskedWalletDetailsButtonTextAppearance 0x0
int attr maskedWalletDetailsHeaderTextAppearance 0x0
int attr maskedWalletDetailsLogoImageType 0x0
int attr maskedWalletDetailsLogoTextColor 0x0
int attr maskedWalletDetailsTextAppearance 0x0
int attr nestedScrollViewStyle 0x0
int attr noIndex 0x0
int attr paramName 0x0
int attr paramValue 0x0
int attr perAccountTemplate 0x0
int attr placeholderActivityName 0x0
int attr primaryActivityName 0x0
int attr queryPatterns 0x0
int attr schemaOrgProperty 0x0
int attr schemaOrgType 0x0
int attr scopeUris 0x0
int attr searchEnabled 0x0
int attr searchLabel 0x0
int attr secondaryActivityAction 0x0
int attr secondaryActivityName 0x0
int attr sectionContent 0x0
int attr sectionFormat 0x0
int attr sectionId 0x0
int attr sectionType 0x0
int attr sectionWeight 0x0
int attr semanticallySearchable 0x0
int attr settingsDescription 0x0
int attr shortcutMatchRequired 0x0
int attr sourceClass 0x0
int attr splitLayoutDirection 0x0
int attr splitMaxAspectRatioInLandscape 0x0
int attr splitMaxAspectRatioInPortrait 0x0
int attr splitMinHeightDp 0x0
int attr splitMinSmallestWidthDp 0x0
int attr splitMinWidthDp 0x0
int attr splitRatio 0x0
int attr statusBarBackground 0x0
int attr stickyPlaceholder 0x0
int attr subsectionSeparator 0x0
int attr tag 0x0
int attr toAddressesSection 0x0
int attr toolbarTextColorStyle 0x0
int attr trimmable 0x0
int attr ttcIndex 0x0
int attr uiCompass 0x0
int attr uiMapToolbar 0x0
int attr uiRotateGestures 0x0
int attr uiScrollGestures 0x0
int attr uiTiltGestures 0x0
int attr uiZoomControls 0x0
int attr uiZoomGestures 0x0
int attr useViewLifecycle 0x0
int attr userInputSection 0x0
int attr userInputTag 0x0
int attr userInputValue 0x0
int attr windowTransitionStyle 0x0
int attr zOrderOnTop 0x0
int color androidx_core_ripple_material_light 0x0
int color androidx_core_secondary_text_default_material_light 0x0
int color call_notification_answer_color 0x0
int color call_notification_decline_color 0x0
int color cast_expanded_controller_ad_container_white_stripe_color 0x0
int color cast_expanded_controller_ad_label_background_color 0x0
int color cast_expanded_controller_background_color 0x0
int color cast_expanded_controller_progress_text_color 0x0
int color cast_expanded_controller_seek_bar_progress_background_tint_color 0x0
int color cast_expanded_controller_text_color 0x0
int color cast_intro_overlay_background_color 0x0
int color cast_intro_overlay_button_background_color 0x0
int color cast_libraries_material_featurehighlight_outer_highlight_default_color 0x0
int color cast_libraries_material_featurehighlight_text_body_color 0x0
int color cast_libraries_material_featurehighlight_text_header_color 0x0
int color common_google_signin_btn_text_dark 0x0
int color common_google_signin_btn_text_dark_default 0x0
int color common_google_signin_btn_text_dark_disabled 0x0
int color common_google_signin_btn_text_dark_focused 0x0
int color common_google_signin_btn_text_dark_pressed 0x0
int color common_google_signin_btn_text_light 0x0
int color common_google_signin_btn_text_light_default 0x0
int color common_google_signin_btn_text_light_disabled 0x0
int color common_google_signin_btn_text_light_focused 0x0
int color common_google_signin_btn_text_light_pressed 0x0
int color common_google_signin_btn_tint 0x0
int color notification_action_color_filter 0x0
int color notification_icon_bg_color 0x0
int color notification_material_background_media_default_color 0x0
int color place_autocomplete_prediction_primary_text 0x0
int color place_autocomplete_prediction_primary_text_highlight 0x0
int color place_autocomplete_prediction_secondary_text 0x0
int color place_autocomplete_search_hint 0x0
int color place_autocomplete_search_text 0x0
int color place_autocomplete_separator 0x0
int color primary_text_default_material_dark 0x0
int color ripple_material_light 0x0
int color secondary_text_default_material_dark 0x0
int color secondary_text_default_material_light 0x0
int color wallet_bright_foreground_disabled_holo_light 0x0
int color wallet_bright_foreground_holo_dark 0x0
int color wallet_bright_foreground_holo_light 0x0
int color wallet_dim_foreground_disabled_holo_dark 0x0
int color wallet_dim_foreground_holo_dark 0x0
int color wallet_highlighted_text_holo_dark 0x0
int color wallet_highlighted_text_holo_light 0x0
int color wallet_hint_foreground_holo_dark 0x0
int color wallet_hint_foreground_holo_light 0x0
int color wallet_holo_blue_light 0x0
int color wallet_link_text_light 0x0
int color wallet_primary_text_holo_light 0x0
int color wallet_secondary_text_holo_dark 0x0
int dimen cast_expanded_controller_ad_background_layout_height 0x0
int dimen cast_expanded_controller_ad_background_layout_width 0x0
int dimen cast_expanded_controller_ad_layout_height 0x0
int dimen cast_expanded_controller_ad_layout_width 0x0
int dimen cast_expanded_controller_control_button_margin 0x0
int dimen cast_expanded_controller_control_toolbar_min_height 0x0
int dimen cast_expanded_controller_margin_between_seek_bar_and_control_buttons 0x0
int dimen cast_expanded_controller_margin_between_status_text_and_seek_bar 0x0
int dimen cast_expanded_controller_seekbar_disabled_alpha 0x0
int dimen cast_intro_overlay_button_margin_bottom 0x0
int dimen cast_intro_overlay_focus_radius 0x0
int dimen cast_intro_overlay_title_margin_top 0x0
int dimen cast_libraries_material_featurehighlight_center_horizontal_offset 0x0
int dimen cast_libraries_material_featurehighlight_center_threshold 0x0
int dimen cast_libraries_material_featurehighlight_inner_margin 0x0
int dimen cast_libraries_material_featurehighlight_inner_radius 0x0
int dimen cast_libraries_material_featurehighlight_outer_padding 0x0
int dimen cast_libraries_material_featurehighlight_text_body_size 0x0
int dimen cast_libraries_material_featurehighlight_text_header_size 0x0
int dimen cast_libraries_material_featurehighlight_text_horizontal_margin 0x0
int dimen cast_libraries_material_featurehighlight_text_horizontal_offset 0x0
int dimen cast_libraries_material_featurehighlight_text_max_width 0x0
int dimen cast_libraries_material_featurehighlight_text_vertical_space 0x0
int dimen cast_mini_controller_control_button_margin 0x0
int dimen cast_mini_controller_icon_height 0x0
int dimen cast_mini_controller_icon_width 0x0
int dimen cast_notification_image_size 0x0
int dimen cast_tracks_chooser_dialog_no_message_text_size 0x0
int dimen cast_tracks_chooser_dialog_row_text_size 0x0
int dimen compat_button_inset_horizontal_material 0x0
int dimen compat_button_inset_vertical_material 0x0
int dimen compat_button_padding_horizontal_material 0x0
int dimen compat_button_padding_vertical_material 0x0
int dimen compat_control_corner_material 0x0
int dimen compat_notification_large_icon_max_height 0x0
int dimen compat_notification_large_icon_max_width 0x0
int dimen notification_action_icon_size 0x0
int dimen notification_action_text_size 0x0
int dimen notification_big_circle_margin 0x0
int dimen notification_content_margin_start 0x0
int dimen notification_large_icon_height 0x0
int dimen notification_large_icon_width 0x0
int dimen notification_main_column_padding_top 0x0
int dimen notification_media_narrow_margin 0x0
int dimen notification_right_icon_size 0x0
int dimen notification_right_side_padding_top 0x0
int dimen notification_small_icon_background_padding 0x0
int dimen notification_small_icon_size_as_large 0x0
int dimen notification_subtext_size 0x0
int dimen notification_top_pad 0x0
int dimen notification_top_pad_large_text 0x0
int dimen place_autocomplete_button_padding 0x0
int dimen place_autocomplete_powered_by_google_height 0x0
int dimen place_autocomplete_powered_by_google_start 0x0
int dimen place_autocomplete_prediction_height 0x0
int dimen place_autocomplete_prediction_horizontal_margin 0x0
int dimen place_autocomplete_prediction_primary_text 0x0
int dimen place_autocomplete_prediction_secondary_text 0x0
int dimen place_autocomplete_progress_horizontal_margin 0x0
int dimen place_autocomplete_progress_size 0x0
int dimen place_autocomplete_separator_start 0x0
int dimen subtitle_corner_radius 0x0
int dimen subtitle_outline_width 0x0
int dimen subtitle_shadow_offset 0x0
int dimen subtitle_shadow_radius 0x0
int drawable cast_abc_scrubber_control_off_mtrl_alpha 0x0
int drawable cast_abc_scrubber_control_to_pressed_mtrl_000 0x0
int drawable cast_abc_scrubber_control_to_pressed_mtrl_005 0x0
int drawable cast_abc_scrubber_primary_mtrl_alpha 0x0
int drawable cast_album_art_placeholder 0x0
int drawable cast_album_art_placeholder_large 0x0
int drawable cast_expanded_controller_actionbar_bg_gradient_light 0x0
int drawable cast_expanded_controller_bg_gradient_light 0x0
int drawable cast_expanded_controller_seekbar_thumb 0x0
int drawable cast_expanded_controller_seekbar_track 0x0
int drawable cast_ic_expanded_controller_closed_caption 0x0
int drawable cast_ic_expanded_controller_forward30 0x0
int drawable cast_ic_expanded_controller_mute 0x0
int drawable cast_ic_expanded_controller_pause 0x0
int drawable cast_ic_expanded_controller_play 0x0
int drawable cast_ic_expanded_controller_rewind30 0x0
int drawable cast_ic_expanded_controller_skip_next 0x0
int drawable cast_ic_expanded_controller_skip_previous 0x0
int drawable cast_ic_expanded_controller_stop 0x0
int drawable cast_ic_mini_controller_closed_caption 0x0
int drawable cast_ic_mini_controller_forward30 0x0
int drawable cast_ic_mini_controller_mute 0x0
int drawable cast_ic_mini_controller_pause 0x0
int drawable cast_ic_mini_controller_pause_large 0x0
int drawable cast_ic_mini_controller_play 0x0
int drawable cast_ic_mini_controller_play_large 0x0
int drawable cast_ic_mini_controller_rewind30 0x0
int drawable cast_ic_mini_controller_skip_next 0x0
int drawable cast_ic_mini_controller_skip_prev 0x0
int drawable cast_ic_mini_controller_stop 0x0
int drawable cast_ic_mini_controller_stop_large 0x0
int drawable cast_ic_notification_0 0x0
int drawable cast_ic_notification_1 0x0
int drawable cast_ic_notification_2 0x0
int drawable cast_ic_notification_connecting 0x0
int drawable cast_ic_notification_disconnect 0x0
int drawable cast_ic_notification_forward 0x0
int drawable cast_ic_notification_forward10 0x0
int drawable cast_ic_notification_forward30 0x0
int drawable cast_ic_notification_on 0x0
int drawable cast_ic_notification_pause 0x0
int drawable cast_ic_notification_play 0x0
int drawable cast_ic_notification_rewind 0x0
int drawable cast_ic_notification_rewind10 0x0
int drawable cast_ic_notification_rewind30 0x0
int drawable cast_ic_notification_skip_next 0x0
int drawable cast_ic_notification_skip_prev 0x0
int drawable cast_ic_notification_small_icon 0x0
int drawable cast_ic_notification_stop_live_stream 0x0
int drawable cast_ic_stop_circle_filled_grey600 0x0
int drawable cast_ic_stop_circle_filled_white 0x0
int drawable cast_mini_controller_gradient_light 0x0
int drawable cast_mini_controller_progress_drawable 0x0
int drawable cast_skip_ad_label_border 0x0
int drawable common_full_open_on_phone 0x0
int drawable common_google_signin_btn_icon_dark 0x0
int drawable common_google_signin_btn_icon_dark_focused 0x0
int drawable common_google_signin_btn_icon_dark_normal 0x0
int drawable common_google_signin_btn_icon_dark_normal_background 0x0
int drawable common_google_signin_btn_icon_disabled 0x0
int drawable common_google_signin_btn_icon_light 0x0
int drawable common_google_signin_btn_icon_light_focused 0x0
int drawable common_google_signin_btn_icon_light_normal 0x0
int drawable common_google_signin_btn_icon_light_normal_background 0x0
int drawable common_google_signin_btn_text_dark 0x0
int drawable common_google_signin_btn_text_dark_focused 0x0
int drawable common_google_signin_btn_text_dark_normal 0x0
int drawable common_google_signin_btn_text_dark_normal_background 0x0
int drawable common_google_signin_btn_text_disabled 0x0
int drawable common_google_signin_btn_text_light 0x0
int drawable common_google_signin_btn_text_light_focused 0x0
int drawable common_google_signin_btn_text_light_normal 0x0
int drawable common_google_signin_btn_text_light_normal_background 0x0
int drawable googleg_disabled_color_18 0x0
int drawable googleg_standard_color_18 0x0
int drawable ic_call_answer 0x0
int drawable ic_call_answer_low 0x0
int drawable ic_call_answer_video 0x0
int drawable ic_call_answer_video_low 0x0
int drawable ic_call_decline 0x0
int drawable ic_call_decline_low 0x0
int drawable ic_plusone_medium_off_client 0x0
int drawable ic_plusone_small_off_client 0x0
int drawable ic_plusone_standard_off_client 0x0
int drawable ic_plusone_tall_off_client 0x0
int drawable ic_profile_cover 0x0
int drawable notification_action_background 0x0
int drawable notification_bg 0x0
int drawable notification_bg_low 0x0
int drawable notification_bg_low_normal 0x0
int drawable notification_bg_low_pressed 0x0
int drawable notification_bg_normal 0x0
int drawable notification_bg_normal_pressed 0x0
int drawable notification_icon_background 0x0
int drawable notification_oversize_large_icon_bg 0x0
int drawable notification_template_icon_bg 0x0
int drawable notification_template_icon_low_bg 0x0
int drawable notification_tile_bg 0x0
int drawable notify_panel_notification_icon_bg 0x0
int drawable places_ic_clear 0x0
int drawable places_ic_search 0x0
int drawable powered_by_google_dark 0x0
int drawable powered_by_google_light 0x0
int drawable quantum_ic_art_track_grey600_48 0x0
int drawable quantum_ic_bigtop_updates_white_24 0x0
int drawable quantum_ic_cast_connected_white_24 0x0
int drawable quantum_ic_cast_white_36 0x0
int drawable quantum_ic_clear_white_24 0x0
int drawable quantum_ic_closed_caption_grey600_36 0x0
int drawable quantum_ic_closed_caption_white_36 0x0
int drawable quantum_ic_forward_10_white_24 0x0
int drawable quantum_ic_forward_30_grey600_36 0x0
int drawable quantum_ic_forward_30_white_24 0x0
int drawable quantum_ic_forward_30_white_36 0x0
int drawable quantum_ic_keyboard_arrow_down_white_36 0x0
int drawable quantum_ic_pause_circle_filled_grey600_36 0x0
int drawable quantum_ic_pause_circle_filled_white_36 0x0
int drawable quantum_ic_pause_grey600_36 0x0
int drawable quantum_ic_pause_grey600_48 0x0
int drawable quantum_ic_pause_white_24 0x0
int drawable quantum_ic_play_arrow_grey600_36 0x0
int drawable quantum_ic_play_arrow_grey600_48 0x0
int drawable quantum_ic_play_arrow_white_24 0x0
int drawable quantum_ic_play_circle_filled_grey600_36 0x0
int drawable quantum_ic_play_circle_filled_white_36 0x0
int drawable quantum_ic_refresh_white_24 0x0
int drawable quantum_ic_replay_10_white_24 0x0
int drawable quantum_ic_replay_30_grey600_36 0x0
int drawable quantum_ic_replay_30_white_24 0x0
int drawable quantum_ic_replay_30_white_36 0x0
int drawable quantum_ic_replay_white_24 0x0
int drawable quantum_ic_skip_next_grey600_36 0x0
int drawable quantum_ic_skip_next_white_24 0x0
int drawable quantum_ic_skip_next_white_36 0x0
int drawable quantum_ic_skip_previous_grey600_36 0x0
int drawable quantum_ic_skip_previous_white_24 0x0
int drawable quantum_ic_skip_previous_white_36 0x0
int drawable quantum_ic_stop_grey600_36 0x0
int drawable quantum_ic_stop_grey600_48 0x0
int drawable quantum_ic_stop_white_24 0x0
int drawable quantum_ic_volume_off_grey600_36 0x0
int drawable quantum_ic_volume_off_white_36 0x0
int drawable quantum_ic_volume_up_grey600_36 0x0
int drawable quantum_ic_volume_up_white_36 0x0
int id accessibility_action_clickable_span 0x0
int id accessibility_custom_action_0 0x0
int id accessibility_custom_action_1 0x0
int id accessibility_custom_action_10 0x0
int id accessibility_custom_action_11 0x0
int id accessibility_custom_action_12 0x0
int id accessibility_custom_action_13 0x0
int id accessibility_custom_action_14 0x0
int id accessibility_custom_action_15 0x0
int id accessibility_custom_action_16 0x0
int id accessibility_custom_action_17 0x0
int id accessibility_custom_action_18 0x0
int id accessibility_custom_action_19 0x0
int id accessibility_custom_action_2 0x0
int id accessibility_custom_action_20 0x0
int id accessibility_custom_action_21 0x0
int id accessibility_custom_action_22 0x0
int id accessibility_custom_action_23 0x0
int id accessibility_custom_action_24 0x0
int id accessibility_custom_action_25 0x0
int id accessibility_custom_action_26 0x0
int id accessibility_custom_action_27 0x0
int id accessibility_custom_action_28 0x0
int id accessibility_custom_action_29 0x0
int id accessibility_custom_action_3 0x0
int id accessibility_custom_action_30 0x0
int id accessibility_custom_action_31 0x0
int id accessibility_custom_action_4 0x0
int id accessibility_custom_action_5 0x0
int id accessibility_custom_action_6 0x0
int id accessibility_custom_action_7 0x0
int id accessibility_custom_action_8 0x0
int id accessibility_custom_action_9 0x0
int id action0 0x0
int id action_container 0x0
int id action_divider 0x0
int id action_image 0x0
int id action_text 0x0
int id actions 0x0
int id ad_choices_container 0x0
int id ad_container 0x0
int id ad_image_view 0x0
int id ad_in_progress_label 0x0
int id ad_label 0x0
int id ad_unit 0x0
int id adjacent 0x0
int id adjust_height 0x0
int id adjust_width 0x0
int id always 0x0
int id alwaysAllow 0x0
int id alwaysDisallow 0x0
int id android_pay 0x0
int id android_pay_dark 0x0
int id android_pay_light 0x0
int id android_pay_light_with_border 0x0
int id androidx_window_activity_scope 0x0
int id async 0x0
int id audio_list_view 0x0
int id auto 0x0
int id background_image_view 0x0
int id background_place_holder_image_view 0x0
int id blocking 0x0
int id blurred_background_image_view 0x0
int id book_now 0x0
int id bottom 0x0
int id bottomToTop 0x0
int id button 0x0
int id button_0 0x0
int id button_1 0x0
int id button_2 0x0
int id button_3 0x0
int id button_play_pause_toggle 0x0
int id buyButton 0x0
int id buy_now 0x0
int id buy_with 0x0
int id buy_with_google 0x0
int id cancel_action 0x0
int id cast_button_type_closed_caption 0x0
int id cast_button_type_custom 0x0
int id cast_button_type_empty 0x0
int id cast_button_type_forward_30_seconds 0x0
int id cast_button_type_mute_toggle 0x0
int id cast_button_type_play_pause_toggle 0x0
int id cast_button_type_rewind_30_seconds 0x0
int id cast_button_type_skip_next 0x0
int id cast_button_type_skip_previous 0x0
int id cast_featurehighlight_help_text_body_view 0x0
int id cast_featurehighlight_help_text_header_view 0x0
int id cast_featurehighlight_view 0x0
int id cast_notification_id 0x0
int id center 0x0
int id chronometer 0x0
int id classic 0x0
int id contact 0x0
int id container_all 0x0
int id container_current 0x0
int id controllers 0x0
int id crash_reporting_present 0x0
int id dark 0x0
int id date 0x0
int id demote_common_words 0x0
int id demote_rfc822_hostnames 0x0
int id dialog_button 0x0
int id donate_with 0x0
int id donate_with_google 0x0
int id edit_text_id 0x0
int id email 0x0
int id end 0x0
int id end_padder 0x0
int id end_text 0x0
int id expanded_controller_layout 0x0
int id forever 0x0
int id fragment_container_view_tag 0x0
int id google_wallet_classic 0x0
int id google_wallet_grayscale 0x0
int id google_wallet_monochrome 0x0
int id grayscale 0x0
int id hide_ime_id 0x0
int id holo_dark 0x0
int id holo_light 0x0
int id html 0x0
int id hybrid 0x0
int id icon 0x0
int id icon_group 0x0
int id icon_only 0x0
int id icon_uri 0x0
int id icon_view 0x0
int id index_entity_types 0x0
int id info 0x0
int id instant_message 0x0
int id intent_action 0x0
int id intent_activity 0x0
int id intent_data 0x0
int id intent_data_id 0x0
int id intent_extra_data 0x0
int id italic 0x0
int id large_icon_uri 0x0
int id left 0x0
int id license 0x0
int id license_activity_scrollview 0x0
int id license_activity_textview 0x0
int id license_list 0x0
int id light 0x0
int id line1 0x0
int id line3 0x0
int id live_stream_indicator 0x0
int id live_stream_seek_bar 0x0
int id loading_indicator 0x0
int id locale 0x0
int id logo_only 0x0
int id ltr 0x0
int id match_global_nicknames 0x0
int id match_parent 0x0
int id media_actions 0x0
int id monochrome 0x0
int id native_ad_body 0x0
int id native_ad_button_text 0x0
int id native_ad_call_to_action 0x0
int id native_ad_container 0x0
int id native_ad_icon 0x0
int id native_ad_main 0x0
int id native_ad_media 0x0
int id native_ad_option 0x0
int id native_ad_social_context 0x0
int id native_ad_sponsored_label 0x0
int id native_ad_title 0x0
int id never 0x0
int id none 0x0
int id normal 0x0
int id notification_background 0x0
int id notification_main_column 0x0
int id notification_main_column_container 0x0
int id omnibox_title_section 0x0
int id omnibox_url_section 0x0
int id place_autocomplete_clear_button 0x0
int id place_autocomplete_powered_by_google 0x0
int id place_autocomplete_prediction_primary_text 0x0
int id place_autocomplete_prediction_secondary_text 0x0
int id place_autocomplete_progress 0x0
int id place_autocomplete_search_button 0x0
int id place_autocomplete_search_input 0x0
int id place_autocomplete_separator 0x0
int id plain 0x0
int id production 0x0
int id profile_filter 0x0
int id progressBar 0x0
int id radio 0x0
int id report_drawn 0x0
int id rfc822 0x0
int id right 0x0
int id right_icon 0x0
int id right_side 0x0
int id rtl 0x0
int id sandbox 0x0
int id satellite 0x0
int id seek_bar 0x0
int id seek_bar_controls 0x0
int id selectionDetails 0x0
int id slide 0x0
int id special_effects_controller_view_tag 0x0
int id standard 0x0
int id start 0x0
int id start_text 0x0
int id status_bar_latest_event_content 0x0
int id status_text 0x0
int id strict_sandbox 0x0
int id subtitle_view 0x0
int id tab_host 0x0
int id tag_accessibility_actions 0x0
int id tag_accessibility_clickable_spans 0x0
int id tag_accessibility_heading 0x0
int id tag_accessibility_pane_title 0x0
int id tag_on_apply_window_listener 0x0
int id tag_on_receive_content_listener 0x0
int id tag_on_receive_content_mime_types 0x0
int id tag_screen_reader_focusable 0x0
int id tag_state_description 0x0
int id tag_transition_group 0x0
int id tag_unhandled_key_event_manager 0x0
int id tag_unhandled_key_listeners 0x0
int id tag_window_insets_animation_callback 0x0
int id terrain 0x0
int id test 0x0
int id text 0x0
int id text1 0x0
int id text2 0x0
int id textTitle 0x0
int id text_list_view 0x0
int id thing_proto 0x0
int id time 0x0
int id title 0x0
int id title_view 0x0
int id toolbar 0x0
int id top 0x0
int id topToBottom 0x0
int id url 0x0
int id view_tree_lifecycle_owner 0x0
int id view_tree_on_back_pressed_dispatcher_owner 0x0
int id view_tree_saved_state_registry_owner 0x0
int id view_tree_view_model_store_owner 0x0
int id visible_removing_fragment_view_tag 0x0
int id wide 0x0
int id wrap_content 0x0
int integer cancel_button_image_alpha 0x0
int integer cast_libraries_material_featurehighlight_pulse_base_alpha 0x0
int integer google_play_services_version 0x0
int integer status_bar_notification_info_maxnum 0x0
int layout cast_expanded_controller_activity 0x0
int layout cast_help_text 0x0
int layout cast_intro_overlay 0x0
int layout cast_mini_controller 0x0
int layout cast_tracks_chooser_dialog_layout 0x0
int layout cast_tracks_chooser_dialog_row_layout 0x0
int layout custom_dialog 0x0
int layout fb_native_ad_container 0x0
int layout fb_native_ad_layout_horizontal 0x0
int layout fb_native_ad_layout_vertical 0x0
int layout fb_native_banner_ad 0x0
int layout fb_native_media_item_ad 0x0
int layout ime_base_split_test_activity 0x0
int layout ime_secondary_split_test_activity 0x0
int layout libraries_social_licenses_license 0x0
int layout libraries_social_licenses_license_activity 0x0
int layout libraries_social_licenses_license_menu_activity 0x0
int layout notification_action 0x0
int layout notification_action_tombstone 0x0
int layout notification_media_action 0x0
int layout notification_media_cancel_action 0x0
int layout notification_template_big_media 0x0
int layout notification_template_big_media_custom 0x0
int layout notification_template_big_media_narrow 0x0
int layout notification_template_big_media_narrow_custom 0x0
int layout notification_template_custom_big 0x0
int layout notification_template_icon_group 0x0
int layout notification_template_lines_media 0x0
int layout notification_template_media 0x0
int layout notification_template_media_custom 0x0
int layout notification_template_part_chronometer 0x0
int layout notification_template_part_time 0x0
int layout place_autocomplete_fragment 0x0
int layout place_autocomplete_item_powered_by_google 0x0
int layout place_autocomplete_item_prediction 0x0
int layout place_autocomplete_progress 0x0
int raw keep_third_party_licenses 0x0
int string androidx_startup 0x0
int string call_notification_answer_action 0x0
int string call_notification_answer_video_action 0x0
int string call_notification_decline_action 0x0
int string call_notification_hang_up_action 0x0
int string call_notification_incoming_text 0x0
int string call_notification_ongoing_text 0x0
int string call_notification_screening_text 0x0
int string cast_ad_label 0x0
int string cast_casting_to_device 0x0
int string cast_closed_captions 0x0
int string cast_closed_captions_unavailable 0x0
int string cast_disconnect 0x0
int string cast_expanded_controller_ad_image_description 0x0
int string cast_expanded_controller_ad_in_progress 0x0
int string cast_expanded_controller_background_image 0x0
int string cast_expanded_controller_live_stream_indicator 0x0
int string cast_expanded_controller_loading 0x0
int string cast_expanded_controller_skip_ad_label 0x0
int string cast_forward 0x0
int string cast_forward_10 0x0
int string cast_forward_30 0x0
int string cast_intro_overlay_button_text 0x0
int string cast_invalid_stream_duration_text 0x0
int string cast_invalid_stream_position_text 0x0
int string cast_mute 0x0
int string cast_notification_connected_message 0x0
int string cast_notification_connecting_message 0x0
int string cast_notification_disconnect 0x0
int string cast_pause 0x0
int string cast_play 0x0
int string cast_rewind 0x0
int string cast_rewind_10 0x0
int string cast_rewind_30 0x0
int string cast_seek_bar 0x0
int string cast_skip_next 0x0
int string cast_skip_prev 0x0
int string cast_stop 0x0
int string cast_stop_live_stream 0x0
int string cast_tracks_chooser_dialog_audio 0x0
int string cast_tracks_chooser_dialog_cancel 0x0
int string cast_tracks_chooser_dialog_closed_captions 0x0
int string cast_tracks_chooser_dialog_default_track_name 0x0
int string cast_tracks_chooser_dialog_none 0x0
int string cast_tracks_chooser_dialog_ok 0x0
int string cast_tracks_chooser_dialog_subtitles 0x0
int string cast_unmute 0x0
int string common_google_play_services_enable_button 0x0
int string common_google_play_services_enable_text 0x0
int string common_google_play_services_enable_title 0x0
int string common_google_play_services_install_button 0x0
int string common_google_play_services_install_text 0x0
int string common_google_play_services_install_title 0x0
int string common_google_play_services_notification_ticker 0x0
int string common_google_play_services_unknown_issue 0x0
int string common_google_play_services_unsupported_text 0x0
int string common_google_play_services_update_button 0x0
int string common_google_play_services_update_text 0x0
int string common_google_play_services_update_title 0x0
int string common_google_play_services_updating_text 0x0
int string common_google_play_services_wear_update_text 0x0
int string common_open_on_phone 0x0
int string common_signin_button_text 0x0
int string common_signin_button_text_long 0x0
int string fcm_fallback_notification_channel_label 0x0
int string license_content_error 0x0
int string place_autocomplete_clear_button 0x0
int string place_autocomplete_search_hint 0x0
int string preferences_license_summary 0x0
int string preferences_license_title 0x0
int string s1 0x0
int string s2 0x0
int string s3 0x0
int string s4 0x0
int string s5 0x0
int string s6 0x0
int string status_bar_notification_info_overflow 0x0
int string tagmanager_preview_dialog_button 0x0
int string tagmanager_preview_dialog_message 0x0
int string tagmanager_preview_dialog_title 0x0
int string wallet_buy_button_place_holder 0x0
int style CastExpandedController 0x0
int style CastIntroOverlay 0x0
int style CastMiniController 0x0
int style CustomCastTheme 0x0
int style TextAppearance_CastIntroOverlay_Button 0x0
int style TextAppearance_CastIntroOverlay_Title 0x0
int style TextAppearance_CastMiniController_Subtitle 0x0
int style TextAppearance_CastMiniController_Title 0x0
int style TextAppearance_Compat_Notification 0x0
int style TextAppearance_Compat_Notification_Info 0x0
int style TextAppearance_Compat_Notification_Info_Media 0x0
int style TextAppearance_Compat_Notification_Line2 0x0
int style TextAppearance_Compat_Notification_Line2_Media 0x0
int style TextAppearance_Compat_Notification_Media 0x0
int style TextAppearance_Compat_Notification_Time 0x0
int style TextAppearance_Compat_Notification_Time_Media 0x0
int style TextAppearance_Compat_Notification_Title 0x0
int style TextAppearance_Compat_Notification_Title_Media 0x0
int style Theme_AppInvite_Preview 0x0
int style Theme_AppInvite_Preview_Base 0x0
int style Theme_IAPTheme 0x0
int style WalletFragmentDefaultButtonTextAppearance 0x0
int style WalletFragmentDefaultDetailsHeaderTextAppearance 0x0
int style WalletFragmentDefaultDetailsTextAppearance 0x0
int style WalletFragmentDefaultStyle 0x0
int style Widget_Compat_NotificationActionContainer 0x0
int style Widget_Compat_NotificationActionText 0x0
int style Widget_Support_CoordinatorLayout 0x0
int[] styleable ActivityFilter { 0x0, 0x0 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x0, 0x0 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable AdsAttrs { 0x0, 0x0, 0x0 }
int styleable AdsAttrs_adSize 0
int styleable AdsAttrs_adSizes 1
int styleable AdsAttrs_adUnitId 2
int[] styleable AppDataSearch {  }
int[] styleable Capability { 0x0, 0x0 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CastExpandedController { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CastExpandedController_castButtonColor 0
int styleable CastExpandedController_castClosedCaptionsButtonDrawable 1
int styleable CastExpandedController_castControlButtons 2
int styleable CastExpandedController_castForward30ButtonDrawable 3
int styleable CastExpandedController_castMuteToggleButtonDrawable 4
int styleable CastExpandedController_castPauseButtonDrawable 5
int styleable CastExpandedController_castPlayButtonDrawable 6
int styleable CastExpandedController_castRewind30ButtonDrawable 7
int styleable CastExpandedController_castSeekBarProgressDrawable 8
int styleable CastExpandedController_castSeekBarThumbDrawable 9
int styleable CastExpandedController_castSkipNextButtonDrawable 10
int styleable CastExpandedController_castSkipPreviousButtonDrawable 11
int styleable CastExpandedController_castStopButtonDrawable 12
int[] styleable CastIntroOverlay { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CastIntroOverlay_castBackgroundColor 0
int styleable CastIntroOverlay_castButtonBackgroundColor 1
int styleable CastIntroOverlay_castButtonText 2
int styleable CastIntroOverlay_castButtonTextAppearance 3
int styleable CastIntroOverlay_castFocusRadius 4
int styleable CastIntroOverlay_castTitleTextAppearance 5
int[] styleable CastMiniController { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CastMiniController_castBackground 0
int styleable CastMiniController_castButtonColor 1
int styleable CastMiniController_castClosedCaptionsButtonDrawable 2
int styleable CastMiniController_castControlButtons 3
int styleable CastMiniController_castForward30ButtonDrawable 4
int styleable CastMiniController_castLargePauseButtonDrawable 5
int styleable CastMiniController_castLargePlayButtonDrawable 6
int styleable CastMiniController_castLargeStopButtonDrawable 7
int styleable CastMiniController_castMuteToggleButtonDrawable 8
int styleable CastMiniController_castPauseButtonDrawable 9
int styleable CastMiniController_castPlayButtonDrawable 10
int styleable CastMiniController_castProgressBarColor 11
int styleable CastMiniController_castRewind30ButtonDrawable 12
int styleable CastMiniController_castShowImageThumbnail 13
int styleable CastMiniController_castSkipNextButtonDrawable 14
int styleable CastMiniController_castSkipPreviousButtonDrawable 15
int styleable CastMiniController_castStopButtonDrawable 16
int styleable CastMiniController_castSubtitleTextAppearance 17
int styleable CastMiniController_castTitleTextAppearance 18
int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
int styleable ColorStateListItem_alpha 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_color 2
int styleable ColorStateListItem_android_lStar 3
int styleable ColorStateListItem_lStar 4
int[] styleable CoordinatorLayout { 0x0, 0x0 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable Corpus { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Corpus_contentProviderUri 0
int styleable Corpus_corpusId 1
int styleable Corpus_corpusVersion 2
int styleable Corpus_documentMaxAgeSecs 3
int styleable Corpus_perAccountTemplate 4
int styleable Corpus_schemaOrgType 5
int styleable Corpus_semanticallySearchable 6
int styleable Corpus_trimmable 7
int[] styleable CustomCastTheme { 0x0, 0x0, 0x0 }
int styleable CustomCastTheme_castExpandedControllerStyle 0
int styleable CustomCastTheme_castIntroOverlayStyle 1
int styleable CustomCastTheme_castMiniControllerStyle 2
int[] styleable CustomWalletTheme { 0x0, 0x0 }
int styleable CustomWalletTheme_toolbarTextColorStyle 0
int styleable CustomWalletTheme_windowTransitionStyle 1
int[] styleable FeatureParam { 0x0, 0x0 }
int styleable FeatureParam_paramName 0
int styleable FeatureParam_paramValue 1
int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontStyle 1
int styleable FontFamilyFont_android_fontVariationSettings 2
int styleable FontFamilyFont_android_fontWeight 3
int styleable FontFamilyFont_android_ttcIndex 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
int styleable Fragment_android_id 0
int styleable Fragment_android_name 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GlobalSearch { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable GlobalSearch_defaultIntentAction 0
int styleable GlobalSearch_defaultIntentActivity 1
int styleable GlobalSearch_defaultIntentData 2
int styleable GlobalSearch_searchEnabled 3
int styleable GlobalSearch_searchLabel 4
int styleable GlobalSearch_settingsDescription 5
int[] styleable GlobalSearchCorpus { 0x0 }
int styleable GlobalSearchCorpus_allowShortcuts 0
int[] styleable GlobalSearchSection { 0x0, 0x0 }
int styleable GlobalSearchSection_sectionContent 0
int styleable GlobalSearchSection_sectionType 1
int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
int styleable GradientColor_android_centerColor 0
int styleable GradientColor_android_centerX 1
int styleable GradientColor_android_centerY 2
int styleable GradientColor_android_endColor 3
int styleable GradientColor_android_endX 4
int styleable GradientColor_android_endY 5
int styleable GradientColor_android_gradientRadius 6
int styleable GradientColor_android_startColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_tileMode 10
int styleable GradientColor_android_type 11
int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable IMECorpus { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable IMECorpus_inputEnabled 0
int styleable IMECorpus_sourceClass 1
int styleable IMECorpus_toAddressesSection 2
int styleable IMECorpus_userInputSection 3
int styleable IMECorpus_userInputTag 4
int styleable IMECorpus_userInputValue 5
int[] styleable LoadingImageView { 0x0, 0x0, 0x0 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MapAttrs { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable MapAttrs_ambientEnabled 0
int styleable MapAttrs_cameraBearing 1
int styleable MapAttrs_cameraMaxZoomPreference 2
int styleable MapAttrs_cameraMinZoomPreference 3
int styleable MapAttrs_cameraTargetLat 4
int styleable MapAttrs_cameraTargetLng 5
int styleable MapAttrs_cameraTilt 6
int styleable MapAttrs_cameraZoom 7
int styleable MapAttrs_latLngBoundsNorthEastLatitude 8
int styleable MapAttrs_latLngBoundsNorthEastLongitude 9
int styleable MapAttrs_latLngBoundsSouthWestLatitude 10
int styleable MapAttrs_latLngBoundsSouthWestLongitude 11
int styleable MapAttrs_liteMode 12
int styleable MapAttrs_mapType 13
int styleable MapAttrs_uiCompass 14
int styleable MapAttrs_uiMapToolbar 15
int styleable MapAttrs_uiRotateGestures 16
int styleable MapAttrs_uiScrollGestures 17
int styleable MapAttrs_uiTiltGestures 18
int styleable MapAttrs_uiZoomControls 19
int styleable MapAttrs_uiZoomGestures 20
int styleable MapAttrs_useViewLifecycle 21
int styleable MapAttrs_zOrderOnTop 22
int[] styleable Section { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable Section_indexPrefixes 0
int styleable Section_noIndex 1
int styleable Section_schemaOrgProperty 2
int styleable Section_sectionFormat 3
int styleable Section_sectionId 4
int styleable Section_sectionWeight 5
int styleable Section_subsectionSeparator 6
int[] styleable SectionFeature { 0x0 }
int styleable SectionFeature_featureType 0
int[] styleable SignInButton { 0x0, 0x0, 0x0 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable SplitPairFilter { 0x0, 0x0, 0x0 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
int[] styleable WalletFragmentOptions { 0x0, 0x0, 0x0, 0x0 }
int styleable WalletFragmentOptions_appTheme 0
int styleable WalletFragmentOptions_environment 1
int styleable WalletFragmentOptions_fragmentMode 2
int styleable WalletFragmentOptions_fragmentStyle 3
int[] styleable WalletFragmentStyle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable WalletFragmentStyle_buyButtonAppearance 0
int styleable WalletFragmentStyle_buyButtonHeight 1
int styleable WalletFragmentStyle_buyButtonText 2
int styleable WalletFragmentStyle_buyButtonWidth 3
int styleable WalletFragmentStyle_maskedWalletDetailsBackground 4
int styleable WalletFragmentStyle_maskedWalletDetailsButtonBackground 5
int styleable WalletFragmentStyle_maskedWalletDetailsButtonTextAppearance 6
int styleable WalletFragmentStyle_maskedWalletDetailsHeaderTextAppearance 7
int styleable WalletFragmentStyle_maskedWalletDetailsLogoImageType 8
int styleable WalletFragmentStyle_maskedWalletDetailsLogoTextColor 9
int styleable WalletFragmentStyle_maskedWalletDetailsTextAppearance 10
