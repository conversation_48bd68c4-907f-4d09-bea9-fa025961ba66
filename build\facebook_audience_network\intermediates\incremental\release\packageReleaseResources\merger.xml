<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res"><file path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\drawable\ic_profile_cover.xml" preprocessing="true" qualifiers=""><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-xxxhdpi\ic_profile_cover.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-mdpi\ic_profile_cover.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-ldpi\ic_profile_cover.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-xxhdpi\ic_profile_cover.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-hdpi\ic_profile_cover.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-xhdpi\ic_profile_cover.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\pngs\release\drawable-anydpi-v24\ic_profile_cover.xml" qualifiers="anydpi-v24" type="drawable"/></file></source><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\rs\release"/><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res"><file name="fb_native_ad_container" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\layout\fb_native_ad_container.xml" qualifiers="" type="layout"/><file name="fb_native_ad_layout_horizontal" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\layout\fb_native_ad_layout_horizontal.xml" qualifiers="" type="layout"/><file name="fb_native_ad_layout_vertical" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\layout\fb_native_ad_layout_vertical.xml" qualifiers="" type="layout"/><file name="fb_native_banner_ad" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\layout\fb_native_banner_ad.xml" qualifiers="" type="layout"/><file name="fb_native_media_item_ad" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\main\res\layout\fb_native_media_item_ad.xml" qualifiers="" type="layout"/></source><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\rs\release"/><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\facebook_audience_network\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\facebook_audience_network-1.0.1\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>