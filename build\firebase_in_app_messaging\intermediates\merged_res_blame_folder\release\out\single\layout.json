[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_list_menu_item_checkbox.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/custom_dialog.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_dialog_title_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_dialog_title_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_alert_dialog_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_alert_dialog_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_big_media_narrow.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_big_media_narrow.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_media.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_media.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_search_view.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_search_view.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/select_dialog_singlechoice_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_screen_content_include.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_screen_content_include.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_lines_media.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_lines_media.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_media_action.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_media_action.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_bar_up_container.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_bar_up_container.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_activity_chooser_view_list_item.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_activity_chooser_view_list_item.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_popup_menu_item_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_bar_title_item.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_bar_title_item.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_mode_bar.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_mode_bar.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/card.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/card.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/modal.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/modal.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_activity_chooser_view.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_activity_chooser_view.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_alert_dialog_title_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_menu_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_menu_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_media_cancel_action.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_media_cancel_action.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/card_portrait_inner.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/card_portrait_inner.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/modal_portrait_inner.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/modal_portrait_inner.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_popup_menu_header_item_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_expanded_menu_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_expanded_menu_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_screen_toolbar.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_screen_toolbar.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_big_media_narrow_custom.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_big_media_narrow_custom.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_menu_item_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_list_menu_item_icon.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_list_menu_item_icon.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_cascading_menu_item_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/browser_actions_context_menu_page.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-browser-1.0.0-23:/layout/browser_actions_context_menu_page.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/support_simple_spinner_dropdown_item.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/image.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/image.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/browser_actions_context_menu_row.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-browser-1.0.0-23:/layout/browser_actions_context_menu_row.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_alert_dialog_button_bar_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_screen_simple.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_screen_simple.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_big_media.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_big_media.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/banner.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/layout/banner.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_tooltip.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_tooltip.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_list_menu_item_radio.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_list_menu_item_radio.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_action_mode_close_item_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_list_menu_item_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_list_menu_item_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/select_dialog_multichoice_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/select_dialog_multichoice_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/abc_select_dialog_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/abc_select_dialog_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_media_custom.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_media_custom.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/notification_template_big_media_custom.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11:/layout/notification_template_big_media_custom.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/layout/select_dialog_item_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/layout/select_dialog_item_material.xml"}]