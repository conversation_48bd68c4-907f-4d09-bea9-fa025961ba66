{"logs": [{"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-36:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f37499733cc5d2d902f654ef669014a9\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "17,2119,2844,2850", "startColumns": "4,4,4,4", "startOffsets": "775,136079,166168,166379", "endLines": "17,2121,2849,2933", "endColumns": "60,12,24,24", "endOffsets": "831,136219,166374,170890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c99b580e39fd4eacdf8bb583a8794ea4\\transformed\\browser-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375", "endColumns": "57,65,62,61,70,71", "endOffsets": "108,174,237,299,370,442"}, "to": {"startLines": "207,208,209,210,344,345", "startColumns": "4,4,4,4,4,4", "startOffsets": "9436,9494,9560,9623,18620,18691", "endColumns": "57,65,62,61,70,71", "endOffsets": "9489,9555,9618,9680,18686,18758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b695fff92e8b8d03c52c14552a09e6c\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,23,29,37,156,168,174,180,181,182,183,184,436,2226,2232,3323,3331,3346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,1018,1191,1410,6828,7142,7330,7517,7570,7630,7682,7727,24456,139747,139942,183806,184088,184702", "endLines": "2,28,36,44,167,173,179,180,181,182,183,184,436,2231,2236,3330,3345,3361", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,1186,1405,1624,7137,7325,7512,7565,7625,7677,7722,7761,24511,139937,140095,184083,184697,185351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "155,195,196,213,214,241,242,349,350,351,352,353,354,355,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,440,441,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,474,503,504,505,506,507,508,509,515,1959,1960,1965,1968,1973,2117,2118,2769,2803,2952,2985,3015,3048", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6768,8431,8503,9800,9865,11533,11602,18933,19003,19071,19143,19213,19274,19348,20441,20502,20563,20625,20689,20751,20812,20880,20980,21040,21106,21179,21248,21305,21357,22128,22200,22276,22341,22400,22459,22519,22579,22639,22699,22759,22819,22879,22939,22999,23059,23118,23178,23238,23298,23358,23418,23478,23538,23598,23658,23718,23777,23837,23897,23956,24015,24074,24133,24192,24665,24700,24984,25039,25102,25157,25215,25273,25334,25397,25454,25505,25555,25616,25673,25739,25773,25808,26457,28476,28543,28615,28684,28753,28827,28899,29394,123557,123674,123941,124234,124501,135940,136012,157411,158538,171467,173198,174198,174880", "endLines": "155,195,196,213,214,241,242,349,350,351,352,353,354,355,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,440,441,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,474,503,504,505,506,507,508,509,515,1959,1963,1965,1971,1973,2117,2118,2774,2812,2984,3005,3047,3053", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "6823,8498,8586,9860,9926,11597,11660,18998,19066,19138,19208,19269,19343,19416,20497,20558,20620,20684,20746,20807,20875,20975,21035,21101,21174,21243,21300,21352,21414,22195,22271,22336,22395,22454,22514,22574,22634,22694,22754,22814,22874,22934,22994,23054,23113,23173,23233,23293,23353,23413,23473,23533,23593,23653,23713,23772,23832,23892,23951,24010,24069,24128,24187,24246,24695,24730,25034,25097,25152,25210,25268,25329,25392,25449,25500,25550,25611,25668,25734,25768,25803,25838,26522,28538,28610,28679,28748,28822,28894,28982,29460,123669,123870,124046,124430,124625,136007,136074,157609,158834,173193,173874,174875,175042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c46a48074684624811ad0afd765b2e05\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "467", "startColumns": "4", "startOffsets": "26032", "endColumns": "49", "endOffsets": "26077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\80593b2da926ebe4e8d028d5af975b35\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "243,385,386,387,388,1964,1966,1967,1972,1974", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "11665,21463,21516,21569,21622,123875,124051,124173,124435,124630", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "11749,21511,21564,21617,21670,123936,124168,124229,124496,124692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0656c23f9d29748157dfc921e0a1bfce\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "464", "startColumns": "4", "startOffsets": "25875", "endColumns": "42", "endOffsets": "25913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\21318d80f816c5a15adc2dee5ce48a9b\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "466", "startColumns": "4", "startOffsets": "25978", "endColumns": "53", "endOffsets": "26027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cbdeaabc5fb39ae13e7527908de0453\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2247,2263,2269,3362,3378", "startColumns": "4,4,4,4,4", "startOffsets": "140617,141042,141220,185356,185767", "endLines": "2262,2268,2278,3377,3381", "endColumns": "24,24,24,24,24", "endOffsets": "141037,141215,141499,185762,185889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,16,19,25,31,40,47,50,59,62,67,77,87,91,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,203,276,348,394,439,483,540,599,670,728,806,882,1175,1536,2032,2412,2510,2960,3037,3313,3861,4413,4622,4953", "endLines": "2,3,4,5,6,7,8,9,10,11,12,15,18,24,30,39,46,49,58,61,66,76,86,90,96,100", "endColumns": "75,71,72,71,45,44,43,56,58,70,57,12,12,12,12,12,12,12,12,12,12,12,12,12,12,22", "endOffsets": "126,198,271,343,389,434,478,535,594,665,723,801,877,1170,1531,2027,2407,2505,2955,3032,3308,3856,4408,4617,4948,5133"}, "to": {"startLines": "342,343,356,357,367,368,384,510,512,513,1603,1604,1607,1610,1616,1622,1631,1638,1641,1650,1653,1658,1668,1678,1682,3256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18472,18548,19421,19494,20350,20396,21419,28987,29211,29270,98355,98413,98491,98567,98860,99221,99717,100097,100195,100645,100722,100998,101546,102098,102307,181631", "endLines": "342,343,356,357,367,368,384,510,512,513,1603,1606,1609,1615,1621,1630,1637,1640,1649,1652,1657,1667,1677,1681,1687,3259", "endColumns": "75,71,72,71,45,44,43,56,58,70,57,12,12,12,12,12,12,12,12,12,12,12,12,12,12,22", "endOffsets": "18543,18615,19489,19561,20391,20436,21458,29039,29265,29336,98408,98486,98562,98855,99216,99712,100092,100190,100640,100717,100993,101541,102093,102302,102633,181811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e731550fcb0230047e8f9df5991942b2\\transformed\\constraintlayout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "2,3,11,12,13,14,15,19,20,21,22,25,26,29,32,33,34,35,36,39,42,43,44,45,50,53,56,57,58,63,64,65,68,71,72,75,78,81,84,85,88,91,92,97,98,103,106,109,110,111,112,113,114,115,116,117,118,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,407,455,507,568,614,741,802,862,932,1065,1133,1262,1388,1450,1515,1583,1650,1773,1898,1965,2030,2095,2276,2397,2518,2584,2651,2861,2930,2996,3121,3247,3314,3440,3567,3692,3819,3884,4010,4133,4198,4406,4473,4653,4773,4893,4958,5020,5082,5144,5203,5263,5324,5385,5444,5819,8395,8527,11791", "endLines": "2,10,11,12,13,14,18,19,20,21,24,25,28,31,32,33,34,35,38,41,42,43,44,49,52,55,56,57,62,63,64,67,70,71,74,77,80,83,84,87,90,91,96,97,102,105,108,109,110,111,112,113,114,115,116,117,126,127,128,129,130", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "111,402,450,502,563,609,736,797,857,927,1060,1128,1257,1383,1445,1510,1578,1645,1768,1893,1960,2025,2090,2271,2392,2513,2579,2646,2856,2925,2991,3116,3242,3309,3435,3562,3687,3814,3879,4005,4128,4193,4401,4468,4648,4768,4888,4953,5015,5077,5139,5198,5258,5319,5380,5439,5814,8390,8522,11786,11894"}, "to": {"startLines": "3,4,13,14,15,16,19,47,48,49,50,53,54,57,60,61,62,63,64,67,70,71,72,73,78,81,84,85,86,91,92,93,96,99,100,103,106,109,112,113,116,119,120,125,126,131,134,137,138,139,140,141,142,143,144,145,146,2841,2842,2843,3054", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,225,568,616,668,729,891,1723,1784,1844,1914,2047,2115,2244,2370,2432,2497,2565,2632,2755,2880,2947,3012,3077,3258,3379,3500,3566,3633,3843,3912,3978,4103,4229,4296,4422,4549,4674,4801,4866,4992,5115,5180,5388,5455,5635,5755,5875,5940,6002,6064,6126,6185,6245,6306,6367,6426,160196,162772,162904,175047", "endLines": "3,11,13,14,15,16,22,47,48,49,52,53,56,59,60,61,62,63,66,69,70,71,72,77,80,83,84,85,90,91,92,95,98,99,102,105,108,111,112,115,118,119,124,125,130,133,136,137,138,139,140,141,142,143,144,145,154,2841,2842,2843,3054", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "220,511,611,663,724,770,1013,1779,1839,1909,2042,2110,2239,2365,2427,2492,2560,2627,2750,2875,2942,3007,3072,3253,3374,3495,3561,3628,3838,3907,3973,4098,4224,4291,4417,4544,4669,4796,4861,4987,5110,5175,5383,5450,5630,5750,5870,5935,5997,6059,6121,6180,6240,6301,6362,6421,6763,162767,162899,166163,175150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "18,45,46,185,186,187,188,189,190,191,192,193,194,197,198,199,200,201,202,203,204,205,206,211,212,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,358,359,360,361,362,363,364,365,366,389,390,391,392,393,394,395,396,432,433,434,435,439,442,443,446,463,469,470,471,472,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,514,516,517,518,519,520,521,529,530,534,538,549,554,560,567,571,575,580,584,588,592,596,600,604,610,614,620,624,630,634,639,643,646,650,656,660,666,670,676,679,683,687,691,695,699,700,701,702,705,708,711,714,718,719,720,721,722,725,727,729,731,736,737,741,747,751,752,754,765,766,770,776,780,781,782,786,813,817,818,822,850,1020,1046,1217,1243,1274,1282,1288,1302,1324,1329,1334,1344,1353,1362,1366,1373,1381,1388,1389,1398,1401,1404,1408,1412,1416,1419,1420,1425,1430,1440,1445,1452,1458,1459,1462,1466,1471,1473,1475,1478,1481,1483,1487,1490,1497,1500,1503,1507,1509,1513,1515,1517,1519,1523,1531,1539,1551,1557,1566,1569,1580,1583,1584,1589,1590,1688,1757,1827,1828,1838,1847,1848,1850,1854,1857,1860,1863,1866,1869,1872,1875,1879,1882,1885,1888,1892,1895,1899,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1925,1927,1928,1929,1930,1931,1932,1933,1934,1936,1937,1939,1940,1942,1944,1945,1947,1948,1949,1950,1951,1952,1954,1955,1956,1957,1958,1975,1977,1979,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1995,1996,1997,1998,1999,2000,2002,2006,2010,2011,2012,2013,2014,2015,2019,2020,2021,2022,2024,2026,2028,2030,2032,2033,2034,2035,2037,2039,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2055,2056,2057,2058,2060,2062,2063,2065,2066,2068,2070,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2085,2086,2087,2088,2090,2091,2092,2093,2094,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2122,2197,2200,2203,2206,2220,2237,2279,2308,2335,2344,2406,2765,2813,2934,3055,3079,3085,3091,3112,3236,3260,3266,3270,3276,3311,3382,3448,3468,3523,3535,3561", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,1629,1674,7766,7807,7862,7921,7983,8064,8125,8200,8276,8353,8591,8676,8758,8834,8910,8987,9065,9171,9277,9356,9685,9742,10180,10254,10329,10394,10460,10520,10581,10653,10726,10793,10861,10920,10979,11038,11097,11156,11210,11264,11317,11371,11425,11479,11754,11828,11907,11980,12054,12125,12197,12269,12342,12399,12457,12530,12604,12678,12753,12825,12898,12968,13039,13099,13160,13229,13298,13368,13442,13518,13582,13659,13735,13812,13877,13946,14023,14098,14167,14235,14312,14378,14439,14536,14601,14670,14769,14840,14899,14957,15014,15073,15137,15208,15280,15352,15424,15496,15563,15631,15699,15758,15821,15885,15975,16066,16126,16192,16259,16325,16395,16459,16512,16579,16640,16707,16820,16878,16941,17006,17071,17146,17219,17291,17340,17401,17462,17523,17585,17649,17713,17777,17842,17905,17965,18026,18092,18151,18211,18273,18344,18404,19566,19652,19739,19829,19916,20004,20086,20169,20259,21675,21727,21785,21830,21896,21960,22017,22074,24251,24308,24356,24405,24631,24735,24782,24938,25843,26146,26210,26272,26332,26527,26601,26671,26749,26803,26873,26958,27006,27052,27113,27176,27242,27306,27377,27440,27505,27569,27630,27691,27743,27816,27890,27959,28034,28108,28182,28323,29341,29465,29543,29633,29721,29817,29907,30489,30578,30825,31106,31772,32057,32450,32927,33149,33371,33647,33874,34104,34334,34564,34794,35021,35440,35666,36091,36321,36749,36968,37251,37459,37590,37817,38243,38468,38895,39116,39541,39661,39937,40238,40562,40853,41167,41304,41435,41540,41782,41949,42153,42361,42632,42744,42856,42961,43078,43292,43438,43578,43664,44012,44100,44346,44764,45013,45095,45193,45785,45885,46137,46561,46816,46910,46999,47236,49260,49502,49604,49857,52013,62545,64061,74692,76220,77977,78603,79023,80084,81349,81605,81841,82388,82882,83487,83685,84265,84829,85204,85322,85860,86017,86213,86486,86742,86912,87053,87117,87482,87849,88525,88789,89127,89480,89574,89760,90066,90328,90453,90580,90819,91030,91149,91342,91519,91974,92155,92277,92536,92649,92836,92938,93045,93174,93449,93957,94453,95330,95624,96194,96343,97075,97247,97331,97667,97759,102638,107884,113273,113335,113913,114497,114588,114701,114930,115090,115242,115413,115579,115748,115915,116078,116321,116491,116664,116835,117109,117308,117513,117843,117927,118023,118119,118217,118317,118419,118521,118623,118725,118827,118927,119023,119135,119264,119387,119518,119649,119747,119861,119955,120095,120229,120325,120437,120537,120653,120749,120861,120961,121101,121237,121401,121531,121689,121839,121980,122124,122259,122371,122521,122649,122777,122913,123045,123175,123305,123417,124697,124843,124987,125125,125191,125281,125357,125461,125551,125653,125761,125869,125969,126049,126141,126239,126349,126427,126533,126625,126729,126839,126961,127124,127281,127361,127461,127551,127661,127751,127992,128086,128192,128284,128384,128496,128610,128726,128842,128936,129050,129162,129264,129384,129506,129588,129692,129812,129938,130036,130130,130218,130330,130446,130568,130680,130855,130971,131057,131149,131261,131385,131452,131578,131646,131774,131918,132046,132115,132210,132325,132438,132537,132646,132757,132868,132969,133074,133174,133304,133395,133518,133612,133724,133810,133914,134010,134098,134216,134320,134424,134550,134638,134746,134846,134936,135046,135130,135232,135316,135370,135434,135540,135626,135736,135820,136224,138840,138958,139073,139153,139514,140100,141504,142848,144209,144597,147372,157276,158839,170895,175155,175906,176168,176368,176747,181025,181816,182045,182196,182411,183494,185894,188920,189664,191795,192135,193446", "endLines": "18,45,46,185,186,187,188,189,190,191,192,193,194,197,198,199,200,201,202,203,204,205,206,211,212,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,358,359,360,361,362,363,364,365,366,389,390,391,392,393,394,395,396,432,433,434,435,439,442,443,446,463,469,470,471,472,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,514,516,517,518,519,520,528,529,533,537,541,553,559,566,570,574,579,583,587,591,595,599,603,609,613,619,623,629,633,638,642,645,649,655,659,665,669,675,678,682,686,690,694,698,699,700,701,704,707,710,713,717,718,719,720,721,724,726,728,730,735,736,740,746,750,751,753,764,765,769,775,779,780,781,785,812,816,817,821,849,1019,1045,1216,1242,1273,1281,1287,1301,1323,1328,1333,1343,1352,1361,1365,1372,1380,1387,1388,1397,1400,1403,1407,1411,1415,1418,1419,1424,1429,1439,1444,1451,1457,1458,1461,1465,1470,1472,1474,1477,1480,1482,1486,1489,1496,1499,1502,1506,1508,1512,1514,1516,1518,1522,1530,1538,1550,1556,1565,1568,1579,1582,1583,1588,1589,1594,1756,1826,1827,1837,1846,1847,1849,1853,1856,1859,1862,1865,1868,1871,1874,1878,1881,1884,1887,1891,1894,1898,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1924,1926,1927,1928,1929,1930,1931,1932,1933,1935,1936,1938,1939,1941,1943,1944,1946,1947,1948,1949,1950,1951,1953,1954,1955,1956,1957,1958,1976,1978,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1994,1995,1996,1997,1998,1999,2001,2005,2009,2010,2011,2012,2013,2014,2018,2019,2020,2021,2023,2025,2027,2029,2031,2032,2033,2034,2036,2038,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2054,2055,2056,2057,2059,2061,2062,2064,2065,2067,2069,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2084,2085,2086,2087,2089,2090,2091,2092,2093,2095,2097,2099,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2196,2199,2202,2205,2219,2225,2246,2307,2334,2343,2405,2764,2768,2840,2951,3078,3084,3090,3111,3235,3255,3265,3269,3275,3310,3322,3447,3467,3522,3534,3560,3567", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "886,1669,1718,7802,7857,7916,7978,8059,8120,8195,8271,8348,8426,8671,8753,8829,8905,8982,9060,9166,9272,9351,9431,9737,9795,10249,10324,10389,10455,10515,10576,10648,10721,10788,10856,10915,10974,11033,11092,11151,11205,11259,11312,11366,11420,11474,11528,11823,11902,11975,12049,12120,12192,12264,12337,12394,12452,12525,12599,12673,12748,12820,12893,12963,13034,13094,13155,13224,13293,13363,13437,13513,13577,13654,13730,13807,13872,13941,14018,14093,14162,14230,14307,14373,14434,14531,14596,14665,14764,14835,14894,14952,15009,15068,15132,15203,15275,15347,15419,15491,15558,15626,15694,15753,15816,15880,15970,16061,16121,16187,16254,16320,16390,16454,16507,16574,16635,16702,16815,16873,16936,17001,17066,17141,17214,17286,17335,17396,17457,17518,17580,17644,17708,17772,17837,17900,17960,18021,18087,18146,18206,18268,18339,18399,18467,19647,19734,19824,19911,19999,20081,20164,20254,20345,21722,21780,21825,21891,21955,22012,22069,22123,24303,24351,24400,24451,24660,24777,24826,24979,25870,26205,26267,26327,26384,26596,26666,26744,26798,26868,26953,27001,27047,27108,27171,27237,27301,27372,27435,27500,27564,27625,27686,27738,27811,27885,27954,28029,28103,28177,28318,28388,29389,29538,29628,29716,29812,29902,30484,30573,30820,31101,31353,32052,32445,32922,33144,33366,33642,33869,34099,34329,34559,34789,35016,35435,35661,36086,36316,36744,36963,37246,37454,37585,37812,38238,38463,38890,39111,39536,39656,39932,40233,40557,40848,41162,41299,41430,41535,41777,41944,42148,42356,42627,42739,42851,42956,43073,43287,43433,43573,43659,44007,44095,44341,44759,45008,45090,45188,45780,45880,46132,46556,46811,46905,46994,47231,49255,49497,49599,49852,52008,62540,64056,74687,76215,77972,78598,79018,80079,81344,81600,81836,82383,82877,83482,83680,84260,84824,85199,85317,85855,86012,86208,86481,86737,86907,87048,87112,87477,87844,88520,88784,89122,89475,89569,89755,90061,90323,90448,90575,90814,91025,91144,91337,91514,91969,92150,92272,92531,92644,92831,92933,93040,93169,93444,93952,94448,95325,95619,96189,96338,97070,97242,97326,97662,97754,98032,107879,113268,113330,113908,114492,114583,114696,114925,115085,115237,115408,115574,115743,115910,116073,116316,116486,116659,116830,117104,117303,117508,117838,117922,118018,118114,118212,118312,118414,118516,118618,118720,118822,118922,119018,119130,119259,119382,119513,119644,119742,119856,119950,120090,120224,120320,120432,120532,120648,120744,120856,120956,121096,121232,121396,121526,121684,121834,121975,122119,122254,122366,122516,122644,122772,122908,123040,123170,123300,123412,123552,124838,124982,125120,125186,125276,125352,125456,125546,125648,125756,125864,125964,126044,126136,126234,126344,126422,126528,126620,126724,126834,126956,127119,127276,127356,127456,127546,127656,127746,127987,128081,128187,128279,128379,128491,128605,128721,128837,128931,129045,129157,129259,129379,129501,129583,129687,129807,129933,130031,130125,130213,130325,130441,130563,130675,130850,130966,131052,131144,131256,131380,131447,131573,131641,131769,131913,132041,132110,132205,132320,132433,132532,132641,132752,132863,132964,133069,133169,133299,133390,133513,133607,133719,133805,133909,134005,134093,134211,134315,134419,134545,134633,134741,134841,134931,135041,135125,135227,135311,135365,135429,135535,135621,135731,135815,135935,138835,138953,139068,139148,139509,139742,140612,142843,144204,144592,147367,157271,157406,160191,171462,175901,176163,176363,176742,181020,181626,182040,182191,182406,183489,183801,188915,189659,191790,192130,193441,193644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e93a2d421962aa211f27e1b69b786d3e\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "437,445,468,3006,3011", "startColumns": "4,4,4,4,4", "startOffsets": "24516,24873,26082,173879,174049", "endLines": "437,445,468,3010,3014", "endColumns": "56,64,63,24,24", "endOffsets": "24568,24933,26141,174044,174193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6ef2c27f4bc3e4a56043fbbc1696599\\transformed\\jetified-glide-4.11.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "438", "startColumns": "4", "startOffsets": "24573", "endColumns": "57", "endOffsets": "24626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\191b3d8ada05bbe09a97b501b2719866\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "12,215,216,217,218,346,347,348,542,1595,1597,1600,2775", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "516,9931,9992,10054,10116,18763,18822,18879,31358,98037,98101,98227,157614", "endLines": "12,215,216,217,218,346,347,348,548,1596,1599,1602,2802", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "563,9987,10049,10111,10175,18817,18874,18928,31767,98096,98222,98350,158533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\067517fbf26e28d3fc19688933378e3c\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "502", "startColumns": "4", "startOffsets": "28393", "endColumns": "82", "endOffsets": "28471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "473,511", "startColumns": "4,4", "startOffsets": "26389,29044", "endColumns": "67,166", "endOffsets": "26452,29206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bce6e27288cd6eb63e66f7f9a4a86f17\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "444,465", "startColumns": "4,4", "startOffsets": "24831,25918", "endColumns": "41,59", "endOffsets": "24868,25973"}}]}, {"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f37499733cc5d2d902f654ef669014a9\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "17,2119,2844,2850", "startColumns": "4,4,4,4", "startOffsets": "775,136079,166168,166379", "endLines": "17,2121,2849,2933", "endColumns": "60,12,24,24", "endOffsets": "831,136219,166374,170890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c99b580e39fd4eacdf8bb583a8794ea4\\transformed\\browser-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375", "endColumns": "57,65,62,61,70,71", "endOffsets": "108,174,237,299,370,442"}, "to": {"startLines": "207,208,209,210,344,345", "startColumns": "4,4,4,4,4,4", "startOffsets": "9436,9494,9560,9623,18620,18691", "endColumns": "57,65,62,61,70,71", "endOffsets": "9489,9555,9618,9680,18686,18758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b695fff92e8b8d03c52c14552a09e6c\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,23,29,37,156,168,174,180,181,182,183,184,436,2226,2232,3323,3331,3346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,1018,1191,1410,6828,7142,7330,7517,7570,7630,7682,7727,24456,139747,139942,183806,184088,184702", "endLines": "2,28,36,44,167,173,179,180,181,182,183,184,436,2231,2236,3330,3345,3361", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,1186,1405,1624,7137,7325,7512,7565,7625,7677,7722,7761,24511,139937,140095,184083,184697,185351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "155,195,196,213,214,241,242,349,350,351,352,353,354,355,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,440,441,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,474,503,504,505,506,507,508,509,515,1959,1960,1965,1968,1973,2117,2118,2769,2803,2952,2985,3015,3048", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6768,8431,8503,9800,9865,11533,11602,18933,19003,19071,19143,19213,19274,19348,20441,20502,20563,20625,20689,20751,20812,20880,20980,21040,21106,21179,21248,21305,21357,22128,22200,22276,22341,22400,22459,22519,22579,22639,22699,22759,22819,22879,22939,22999,23059,23118,23178,23238,23298,23358,23418,23478,23538,23598,23658,23718,23777,23837,23897,23956,24015,24074,24133,24192,24665,24700,24984,25039,25102,25157,25215,25273,25334,25397,25454,25505,25555,25616,25673,25739,25773,25808,26457,28476,28543,28615,28684,28753,28827,28899,29394,123557,123674,123941,124234,124501,135940,136012,157411,158538,171467,173198,174198,174880", "endLines": "155,195,196,213,214,241,242,349,350,351,352,353,354,355,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,440,441,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,474,503,504,505,506,507,508,509,515,1959,1963,1965,1971,1973,2117,2118,2774,2812,2984,3005,3047,3053", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "6823,8498,8586,9860,9926,11597,11660,18998,19066,19138,19208,19269,19343,19416,20497,20558,20620,20684,20746,20807,20875,20975,21035,21101,21174,21243,21300,21352,21414,22195,22271,22336,22395,22454,22514,22574,22634,22694,22754,22814,22874,22934,22994,23054,23113,23173,23233,23293,23353,23413,23473,23533,23593,23653,23713,23772,23832,23892,23951,24010,24069,24128,24187,24246,24695,24730,25034,25097,25152,25210,25268,25329,25392,25449,25500,25550,25611,25668,25734,25768,25803,25838,26522,28538,28610,28679,28748,28822,28894,28982,29460,123669,123870,124046,124430,124625,136007,136074,157609,158834,173193,173874,174875,175042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c46a48074684624811ad0afd765b2e05\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "467", "startColumns": "4", "startOffsets": "26032", "endColumns": "49", "endOffsets": "26077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\80593b2da926ebe4e8d028d5af975b35\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "243,385,386,387,388,1964,1966,1967,1972,1974", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "11665,21463,21516,21569,21622,123875,124051,124173,124435,124630", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "11749,21511,21564,21617,21670,123936,124168,124229,124496,124692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0656c23f9d29748157dfc921e0a1bfce\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "464", "startColumns": "4", "startOffsets": "25875", "endColumns": "42", "endOffsets": "25913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\21318d80f816c5a15adc2dee5ce48a9b\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "466", "startColumns": "4", "startOffsets": "25978", "endColumns": "53", "endOffsets": "26027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cbdeaabc5fb39ae13e7527908de0453\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2247,2263,2269,3362,3378", "startColumns": "4,4,4,4,4", "startOffsets": "140617,141042,141220,185356,185767", "endLines": "2262,2268,2278,3377,3381", "endColumns": "24,24,24,24,24", "endOffsets": "141037,141215,141499,185762,185889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,16,19,25,31,40,47,50,59,62,67,77,87,91,97", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,203,276,348,394,439,483,540,599,670,728,806,882,1175,1536,2032,2412,2510,2960,3037,3313,3861,4413,4622,4953", "endLines": "2,3,4,5,6,7,8,9,10,11,12,15,18,24,30,39,46,49,58,61,66,76,86,90,96,100", "endColumns": "75,71,72,71,45,44,43,56,58,70,57,12,12,12,12,12,12,12,12,12,12,12,12,12,12,22", "endOffsets": "126,198,271,343,389,434,478,535,594,665,723,801,877,1170,1531,2027,2407,2505,2955,3032,3308,3856,4408,4617,4948,5133"}, "to": {"startLines": "342,343,356,357,367,368,384,510,512,513,1603,1604,1607,1610,1616,1622,1631,1638,1641,1650,1653,1658,1668,1678,1682,3256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18472,18548,19421,19494,20350,20396,21419,28987,29211,29270,98355,98413,98491,98567,98860,99221,99717,100097,100195,100645,100722,100998,101546,102098,102307,181631", "endLines": "342,343,356,357,367,368,384,510,512,513,1603,1606,1609,1615,1621,1630,1637,1640,1649,1652,1657,1667,1677,1681,1687,3259", "endColumns": "75,71,72,71,45,44,43,56,58,70,57,12,12,12,12,12,12,12,12,12,12,12,12,12,12,22", "endOffsets": "18543,18615,19489,19561,20391,20436,21458,29039,29265,29336,98408,98486,98562,98855,99216,99712,100092,100190,100640,100717,100993,101541,102093,102302,102633,181811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e731550fcb0230047e8f9df5991942b2\\transformed\\constraintlayout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "2,3,11,12,13,14,15,19,20,21,22,25,26,29,32,33,34,35,36,39,42,43,44,45,50,53,56,57,58,63,64,65,68,71,72,75,78,81,84,85,88,91,92,97,98,103,106,109,110,111,112,113,114,115,116,117,118,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,407,455,507,568,614,741,802,862,932,1065,1133,1262,1388,1450,1515,1583,1650,1773,1898,1965,2030,2095,2276,2397,2518,2584,2651,2861,2930,2996,3121,3247,3314,3440,3567,3692,3819,3884,4010,4133,4198,4406,4473,4653,4773,4893,4958,5020,5082,5144,5203,5263,5324,5385,5444,5819,8395,8527,11791", "endLines": "2,10,11,12,13,14,18,19,20,21,24,25,28,31,32,33,34,35,38,41,42,43,44,49,52,55,56,57,62,63,64,67,70,71,74,77,80,83,84,87,90,91,96,97,102,105,108,109,110,111,112,113,114,115,116,117,126,127,128,129,130", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "111,402,450,502,563,609,736,797,857,927,1060,1128,1257,1383,1445,1510,1578,1645,1768,1893,1960,2025,2090,2271,2392,2513,2579,2646,2856,2925,2991,3116,3242,3309,3435,3562,3687,3814,3879,4005,4128,4193,4401,4468,4648,4768,4888,4953,5015,5077,5139,5198,5258,5319,5380,5439,5814,8390,8522,11786,11894"}, "to": {"startLines": "3,4,13,14,15,16,19,47,48,49,50,53,54,57,60,61,62,63,64,67,70,71,72,73,78,81,84,85,86,91,92,93,96,99,100,103,106,109,112,113,116,119,120,125,126,131,134,137,138,139,140,141,142,143,144,145,146,2841,2842,2843,3054", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "164,225,568,616,668,729,891,1723,1784,1844,1914,2047,2115,2244,2370,2432,2497,2565,2632,2755,2880,2947,3012,3077,3258,3379,3500,3566,3633,3843,3912,3978,4103,4229,4296,4422,4549,4674,4801,4866,4992,5115,5180,5388,5455,5635,5755,5875,5940,6002,6064,6126,6185,6245,6306,6367,6426,160196,162772,162904,175047", "endLines": "3,11,13,14,15,16,22,47,48,49,52,53,56,59,60,61,62,63,66,69,70,71,72,77,80,83,84,85,90,91,92,95,98,99,102,105,108,111,112,115,118,119,124,125,130,133,136,137,138,139,140,141,142,143,144,145,154,2841,2842,2843,3054", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "220,511,611,663,724,770,1013,1779,1839,1909,2042,2110,2239,2365,2427,2492,2560,2627,2750,2875,2942,3007,3072,3253,3374,3495,3561,3628,3838,3907,3973,4098,4224,4291,4417,4544,4669,4796,4861,4987,5110,5175,5383,5450,5630,5750,5870,5935,5997,6059,6121,6180,6240,6301,6362,6421,6763,162767,162899,166163,175150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "18,45,46,185,186,187,188,189,190,191,192,193,194,197,198,199,200,201,202,203,204,205,206,211,212,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,358,359,360,361,362,363,364,365,366,389,390,391,392,393,394,395,396,432,433,434,435,439,442,443,446,463,469,470,471,472,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,514,516,517,518,519,520,521,529,530,534,538,549,554,560,567,571,575,580,584,588,592,596,600,604,610,614,620,624,630,634,639,643,646,650,656,660,666,670,676,679,683,687,691,695,699,700,701,702,705,708,711,714,718,719,720,721,722,725,727,729,731,736,737,741,747,751,752,754,765,766,770,776,780,781,782,786,813,817,818,822,850,1020,1046,1217,1243,1274,1282,1288,1302,1324,1329,1334,1344,1353,1362,1366,1373,1381,1388,1389,1398,1401,1404,1408,1412,1416,1419,1420,1425,1430,1440,1445,1452,1458,1459,1462,1466,1471,1473,1475,1478,1481,1483,1487,1490,1497,1500,1503,1507,1509,1513,1515,1517,1519,1523,1531,1539,1551,1557,1566,1569,1580,1583,1584,1589,1590,1688,1757,1827,1828,1838,1847,1848,1850,1854,1857,1860,1863,1866,1869,1872,1875,1879,1882,1885,1888,1892,1895,1899,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1925,1927,1928,1929,1930,1931,1932,1933,1934,1936,1937,1939,1940,1942,1944,1945,1947,1948,1949,1950,1951,1952,1954,1955,1956,1957,1958,1975,1977,1979,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1995,1996,1997,1998,1999,2000,2002,2006,2010,2011,2012,2013,2014,2015,2019,2020,2021,2022,2024,2026,2028,2030,2032,2033,2034,2035,2037,2039,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2055,2056,2057,2058,2060,2062,2063,2065,2066,2068,2070,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2085,2086,2087,2088,2090,2091,2092,2093,2094,2096,2098,2100,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2122,2197,2200,2203,2206,2220,2237,2279,2308,2335,2344,2406,2765,2813,2934,3055,3079,3085,3091,3112,3236,3260,3266,3270,3276,3311,3382,3448,3468,3523,3535,3561", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,1629,1674,7766,7807,7862,7921,7983,8064,8125,8200,8276,8353,8591,8676,8758,8834,8910,8987,9065,9171,9277,9356,9685,9742,10180,10254,10329,10394,10460,10520,10581,10653,10726,10793,10861,10920,10979,11038,11097,11156,11210,11264,11317,11371,11425,11479,11754,11828,11907,11980,12054,12125,12197,12269,12342,12399,12457,12530,12604,12678,12753,12825,12898,12968,13039,13099,13160,13229,13298,13368,13442,13518,13582,13659,13735,13812,13877,13946,14023,14098,14167,14235,14312,14378,14439,14536,14601,14670,14769,14840,14899,14957,15014,15073,15137,15208,15280,15352,15424,15496,15563,15631,15699,15758,15821,15885,15975,16066,16126,16192,16259,16325,16395,16459,16512,16579,16640,16707,16820,16878,16941,17006,17071,17146,17219,17291,17340,17401,17462,17523,17585,17649,17713,17777,17842,17905,17965,18026,18092,18151,18211,18273,18344,18404,19566,19652,19739,19829,19916,20004,20086,20169,20259,21675,21727,21785,21830,21896,21960,22017,22074,24251,24308,24356,24405,24631,24735,24782,24938,25843,26146,26210,26272,26332,26527,26601,26671,26749,26803,26873,26958,27006,27052,27113,27176,27242,27306,27377,27440,27505,27569,27630,27691,27743,27816,27890,27959,28034,28108,28182,28323,29341,29465,29543,29633,29721,29817,29907,30489,30578,30825,31106,31772,32057,32450,32927,33149,33371,33647,33874,34104,34334,34564,34794,35021,35440,35666,36091,36321,36749,36968,37251,37459,37590,37817,38243,38468,38895,39116,39541,39661,39937,40238,40562,40853,41167,41304,41435,41540,41782,41949,42153,42361,42632,42744,42856,42961,43078,43292,43438,43578,43664,44012,44100,44346,44764,45013,45095,45193,45785,45885,46137,46561,46816,46910,46999,47236,49260,49502,49604,49857,52013,62545,64061,74692,76220,77977,78603,79023,80084,81349,81605,81841,82388,82882,83487,83685,84265,84829,85204,85322,85860,86017,86213,86486,86742,86912,87053,87117,87482,87849,88525,88789,89127,89480,89574,89760,90066,90328,90453,90580,90819,91030,91149,91342,91519,91974,92155,92277,92536,92649,92836,92938,93045,93174,93449,93957,94453,95330,95624,96194,96343,97075,97247,97331,97667,97759,102638,107884,113273,113335,113913,114497,114588,114701,114930,115090,115242,115413,115579,115748,115915,116078,116321,116491,116664,116835,117109,117308,117513,117843,117927,118023,118119,118217,118317,118419,118521,118623,118725,118827,118927,119023,119135,119264,119387,119518,119649,119747,119861,119955,120095,120229,120325,120437,120537,120653,120749,120861,120961,121101,121237,121401,121531,121689,121839,121980,122124,122259,122371,122521,122649,122777,122913,123045,123175,123305,123417,124697,124843,124987,125125,125191,125281,125357,125461,125551,125653,125761,125869,125969,126049,126141,126239,126349,126427,126533,126625,126729,126839,126961,127124,127281,127361,127461,127551,127661,127751,127992,128086,128192,128284,128384,128496,128610,128726,128842,128936,129050,129162,129264,129384,129506,129588,129692,129812,129938,130036,130130,130218,130330,130446,130568,130680,130855,130971,131057,131149,131261,131385,131452,131578,131646,131774,131918,132046,132115,132210,132325,132438,132537,132646,132757,132868,132969,133074,133174,133304,133395,133518,133612,133724,133810,133914,134010,134098,134216,134320,134424,134550,134638,134746,134846,134936,135046,135130,135232,135316,135370,135434,135540,135626,135736,135820,136224,138840,138958,139073,139153,139514,140100,141504,142848,144209,144597,147372,157276,158839,170895,175155,175906,176168,176368,176747,181025,181816,182045,182196,182411,183494,185894,188920,189664,191795,192135,193446", "endLines": "18,45,46,185,186,187,188,189,190,191,192,193,194,197,198,199,200,201,202,203,204,205,206,211,212,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,358,359,360,361,362,363,364,365,366,389,390,391,392,393,394,395,396,432,433,434,435,439,442,443,446,463,469,470,471,472,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,514,516,517,518,519,520,528,529,533,537,541,553,559,566,570,574,579,583,587,591,595,599,603,609,613,619,623,629,633,638,642,645,649,655,659,665,669,675,678,682,686,690,694,698,699,700,701,704,707,710,713,717,718,719,720,721,724,726,728,730,735,736,740,746,750,751,753,764,765,769,775,779,780,781,785,812,816,817,821,849,1019,1045,1216,1242,1273,1281,1287,1301,1323,1328,1333,1343,1352,1361,1365,1372,1380,1387,1388,1397,1400,1403,1407,1411,1415,1418,1419,1424,1429,1439,1444,1451,1457,1458,1461,1465,1470,1472,1474,1477,1480,1482,1486,1489,1496,1499,1502,1506,1508,1512,1514,1516,1518,1522,1530,1538,1550,1556,1565,1568,1579,1582,1583,1588,1589,1594,1756,1826,1827,1837,1846,1847,1849,1853,1856,1859,1862,1865,1868,1871,1874,1878,1881,1884,1887,1891,1894,1898,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1924,1926,1927,1928,1929,1930,1931,1932,1933,1935,1936,1938,1939,1941,1943,1944,1946,1947,1948,1949,1950,1951,1953,1954,1955,1956,1957,1958,1976,1978,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1994,1995,1996,1997,1998,1999,2001,2005,2009,2010,2011,2012,2013,2014,2018,2019,2020,2021,2023,2025,2027,2029,2031,2032,2033,2034,2036,2038,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2054,2055,2056,2057,2059,2061,2062,2064,2065,2067,2069,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2084,2085,2086,2087,2089,2090,2091,2092,2093,2095,2097,2099,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2196,2199,2202,2205,2219,2225,2246,2307,2334,2343,2405,2764,2768,2840,2951,3078,3084,3090,3111,3235,3255,3265,3269,3275,3310,3322,3447,3467,3522,3534,3560,3567", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "886,1669,1718,7802,7857,7916,7978,8059,8120,8195,8271,8348,8426,8671,8753,8829,8905,8982,9060,9166,9272,9351,9431,9737,9795,10249,10324,10389,10455,10515,10576,10648,10721,10788,10856,10915,10974,11033,11092,11151,11205,11259,11312,11366,11420,11474,11528,11823,11902,11975,12049,12120,12192,12264,12337,12394,12452,12525,12599,12673,12748,12820,12893,12963,13034,13094,13155,13224,13293,13363,13437,13513,13577,13654,13730,13807,13872,13941,14018,14093,14162,14230,14307,14373,14434,14531,14596,14665,14764,14835,14894,14952,15009,15068,15132,15203,15275,15347,15419,15491,15558,15626,15694,15753,15816,15880,15970,16061,16121,16187,16254,16320,16390,16454,16507,16574,16635,16702,16815,16873,16936,17001,17066,17141,17214,17286,17335,17396,17457,17518,17580,17644,17708,17772,17837,17900,17960,18021,18087,18146,18206,18268,18339,18399,18467,19647,19734,19824,19911,19999,20081,20164,20254,20345,21722,21780,21825,21891,21955,22012,22069,22123,24303,24351,24400,24451,24660,24777,24826,24979,25870,26205,26267,26327,26384,26596,26666,26744,26798,26868,26953,27001,27047,27108,27171,27237,27301,27372,27435,27500,27564,27625,27686,27738,27811,27885,27954,28029,28103,28177,28318,28388,29389,29538,29628,29716,29812,29902,30484,30573,30820,31101,31353,32052,32445,32922,33144,33366,33642,33869,34099,34329,34559,34789,35016,35435,35661,36086,36316,36744,36963,37246,37454,37585,37812,38238,38463,38890,39111,39536,39656,39932,40233,40557,40848,41162,41299,41430,41535,41777,41944,42148,42356,42627,42739,42851,42956,43073,43287,43433,43573,43659,44007,44095,44341,44759,45008,45090,45188,45780,45880,46132,46556,46811,46905,46994,47231,49255,49497,49599,49852,52008,62540,64056,74687,76215,77972,78598,79018,80079,81344,81600,81836,82383,82877,83482,83680,84260,84824,85199,85317,85855,86012,86208,86481,86737,86907,87048,87112,87477,87844,88520,88784,89122,89475,89569,89755,90061,90323,90448,90575,90814,91025,91144,91337,91514,91969,92150,92272,92531,92644,92831,92933,93040,93169,93444,93952,94448,95325,95619,96189,96338,97070,97242,97326,97662,97754,98032,107879,113268,113330,113908,114492,114583,114696,114925,115085,115237,115408,115574,115743,115910,116073,116316,116486,116659,116830,117104,117303,117508,117838,117922,118018,118114,118212,118312,118414,118516,118618,118720,118822,118922,119018,119130,119259,119382,119513,119644,119742,119856,119950,120090,120224,120320,120432,120532,120648,120744,120856,120956,121096,121232,121396,121526,121684,121834,121975,122119,122254,122366,122516,122644,122772,122908,123040,123170,123300,123412,123552,124838,124982,125120,125186,125276,125352,125456,125546,125648,125756,125864,125964,126044,126136,126234,126344,126422,126528,126620,126724,126834,126956,127119,127276,127356,127456,127546,127656,127746,127987,128081,128187,128279,128379,128491,128605,128721,128837,128931,129045,129157,129259,129379,129501,129583,129687,129807,129933,130031,130125,130213,130325,130441,130563,130675,130850,130966,131052,131144,131256,131380,131447,131573,131641,131769,131913,132041,132110,132205,132320,132433,132532,132641,132752,132863,132964,133069,133169,133299,133390,133513,133607,133719,133805,133909,134005,134093,134211,134315,134419,134545,134633,134741,134841,134931,135041,135125,135227,135311,135365,135429,135535,135621,135731,135815,135935,138835,138953,139068,139148,139509,139742,140612,142843,144204,144592,147367,157271,157406,160191,171462,175901,176163,176363,176742,181020,181626,182040,182191,182406,183489,183801,188915,189659,191790,192130,193441,193644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e93a2d421962aa211f27e1b69b786d3e\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "437,445,468,3006,3011", "startColumns": "4,4,4,4,4", "startOffsets": "24516,24873,26082,173879,174049", "endLines": "437,445,468,3010,3014", "endColumns": "56,64,63,24,24", "endOffsets": "24568,24933,26141,174044,174193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6ef2c27f4bc3e4a56043fbbc1696599\\transformed\\jetified-glide-4.11.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "438", "startColumns": "4", "startOffsets": "24573", "endColumns": "57", "endOffsets": "24626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\191b3d8ada05bbe09a97b501b2719866\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "12,215,216,217,218,346,347,348,542,1595,1597,1600,2775", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "516,9931,9992,10054,10116,18763,18822,18879,31358,98037,98101,98227,157614", "endLines": "12,215,216,217,218,346,347,348,548,1596,1599,1602,2802", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "563,9987,10049,10111,10175,18817,18874,18928,31767,98096,98222,98350,158533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\067517fbf26e28d3fc19688933378e3c\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "502", "startColumns": "4", "startOffsets": "28393", "endColumns": "82", "endOffsets": "28471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "473,511", "startColumns": "4,4", "startOffsets": "26389,29044", "endColumns": "67,166", "endOffsets": "26452,29206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bce6e27288cd6eb63e66f7f9a4a86f17\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "444,465", "startColumns": "4,4", "startOffsets": "24831,25918", "endColumns": "41,59", "endOffsets": "24868,25973"}}]}]}