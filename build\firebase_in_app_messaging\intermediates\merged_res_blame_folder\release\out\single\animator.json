[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_open_exit.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_open_exit.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_open_enter.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_open_enter.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_fade_exit.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_fade_exit.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_close_enter.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_close_enter.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_fade_enter.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_fade_enter.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/animator/fragment_close_exit.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26:/animator/fragment_close_exit.xml"}]