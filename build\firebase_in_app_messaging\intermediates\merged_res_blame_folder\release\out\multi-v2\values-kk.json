{"logs": [{"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3510", "endColumns": "163", "endOffsets": "3669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,3674", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,3750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "29,30,31,32,33,34,35,38", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2891,2993,3095,3198,3302,3399,3755", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "2886,2988,3090,3193,3297,3394,3505,3851"}}]}, {"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-36:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2b4f149415a6b8de84f08bfbd57a729e\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3510", "endColumns": "163", "endOffsets": "3669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,896,987,1079,1174,1268,1369,1462,1557,1654,1745,1836,1916,2021,2124,2222,2329,2435,2535,2701,3674", "endColumns": "107,104,109,84,105,118,79,77,90,91,94,93,100,92,94,96,90,90,79,104,102,97,106,105,99,165,94,80", "endOffsets": "208,313,423,508,614,733,813,891,982,1074,1169,1263,1364,1457,1552,1649,1740,1831,1911,2016,2119,2217,2324,2430,2530,2696,2791,3750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a7c6bac0ab70d3396b83193d45a89ada\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "29,30,31,32,33,34,35,38", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2796,2891,2993,3095,3198,3302,3399,3755", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "2886,2988,3090,3193,3297,3394,3505,3851"}}]}]}