{"logs": [{"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-36:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,131,203,276", "endColumns": "75,71,72,71", "endOffsets": "126,198,271,343"}, "to": {"startLines": "5,6,7,8", "startColumns": "4,4,4,4", "startOffsets": "264,340,412,485", "endColumns": "75,71,72,71", "endOffsets": "335,407,480,552"}}]}, {"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/values-land/values-land.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,131,203,276", "endColumns": "75,71,72,71", "endOffsets": "126,198,271,343"}, "to": {"startLines": "5,6,7,8", "startColumns": "4,4,4,4", "startOffsets": "264,340,412,485", "endColumns": "75,71,72,71", "endOffsets": "335,407,480,552"}}]}]}