[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/notification_action_background.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-v21/notification_action_background.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/abc_dialog_material_background.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable-v21/abc_dialog_material_background.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/abc_edit_text_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable-v21/abc_edit_text_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/abc_btn_colored_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable-v21/abc_btn_colored_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/abc_list_divider_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable-v21/abc_list_divider_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-v21/abc_action_bar_item_background_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable-v21/abc_action_bar_item_background_material.xml"}]