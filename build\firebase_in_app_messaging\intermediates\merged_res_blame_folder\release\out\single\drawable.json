[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_vector_test.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-appcompat-resources-1.1.0-8:/drawable/abc_vector_test.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/rounded_layout.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/drawable/rounded_layout.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ratingbar_indicator_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ratingbar_indicator_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_radio_off_to_on_mtrl_animation.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_radio_on_to_off_mtrl_animation.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_go_search_api_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_go_search_api_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/tooltip_frame_dark.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/tooltip_frame_dark.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_spinner_textfield_background_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_spinner_textfield_background_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_cab_background_top_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_cab_background_top_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_list_selector_background_transition_holo_dark.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_voice_search_api_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_voice_search_api_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_textfield_search_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_textfield_search_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_clear.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/drawable/btn_clear.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable/notification_icon_background.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_item_background_holo_dark.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_item_background_holo_dark.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_list_selector_holo_dark.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_list_selector_holo_dark.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_checkbox_unchecked_mtrl.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_checkbox_unchecked_mtrl.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_text_cursor_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_text_cursor_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/notification_bg.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_list_selector_holo_light.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_list_selector_holo_light.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_checkbox_checked_mtrl.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_checkbox_checked_mtrl.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ratingbar_small_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ratingbar_small_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_check_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_check_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/collapse.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/drawable/collapse.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_default_mtrl_shape.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_default_mtrl_shape.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_radio_on_mtrl.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_radio_on_mtrl.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_list_selector_background_transition_holo_light.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_list_selector_background_transition_holo_light.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_radio_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_radio_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_menu_overflow_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_menu_overflow_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_check_material_anim.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_check_material_anim.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_arrow_drop_right_black_24dp.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ratingbar_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ratingbar_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_clear_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_clear_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_item_background_holo_light.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_item_background_holo_light.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/image_placeholder.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3:/drawable/image_placeholder.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_switch_thumb_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_switch_thumb_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_tab_indicator_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_tab_indicator_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_seekbar_tick_mark_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_seekbar_tick_mark_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_seekbar_track_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_seekbar_track_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_ab_back_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_ab_back_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_radio_material_anim.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_radio_material_anim.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_seekbar_thumb_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_seekbar_thumb_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_cab_background_internal_bg.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_cab_background_internal_bg.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_radio_off_mtrl.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_radio_off_mtrl.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_btn_borderless_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_btn_borderless_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable/notification_bg_low.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/abc_ic_search_api_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/abc_ic_search_api_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable/tooltip_frame_light.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/drawable/tooltip_frame_light.xml"}]