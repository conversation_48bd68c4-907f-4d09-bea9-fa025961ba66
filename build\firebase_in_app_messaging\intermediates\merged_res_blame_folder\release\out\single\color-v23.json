[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_spinner.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_spinner.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_btn_checkable.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_btn_checkable.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_color_highlight_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_color_highlight_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_btn_colored_borderless_text_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_btn_colored_borderless_text_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_default.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_default.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_edittext.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_edittext.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_seek_thumb.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_seek_thumb.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_btn_colored_text_material.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_btn_colored_text_material.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/color-v23/abc_tint_switch_track.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30:/color-v23/abc_tint_switch_track.xml"}]