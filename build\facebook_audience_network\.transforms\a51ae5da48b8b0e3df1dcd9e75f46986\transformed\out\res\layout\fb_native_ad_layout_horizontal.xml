<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_vertical"
    android:layout_gravity="center_vertical"
    android:background="@android:color/black">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="10dp">

            <com.facebook.ads.MediaView
                android:id="@+id/native_ad_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/profile_filter"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"/>
        </FrameLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_marginLeft="10dp">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/native_ad_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="12dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/native_ad_sponsored_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="10dp"
                    android:textColor="#A1ACC0" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/native_ad_option"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="left|bottom"
                android:gravity="left|bottom"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:paddingRight="4dp"
                android:background="#00FFFFFF"
                android:orientation="horizontal">
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:gravity="center_vertical|right">
            <LinearLayout
                android:id="@+id/native_ad_call_to_action"
                android:layout_width="100dp"
                android:layout_height="30dp"
                android:gravity="center"
                android:background="#000000">
                <TextView
                    android:id="@+id/native_ad_button_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textSize="13dp"
                    android:textStyle="bold"
                    android:textColor="#000000" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_weight="1">

        <com.facebook.ads.MediaView
            android:id="@+id/native_ad_media"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/native_ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:textColor="@android:color/white"
            android:textSize="14dp"
            android:textStyle="normal" />
    </LinearLayout>
</LinearLayout>