[{"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_answer.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_answer.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_decline.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_decline.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_answer_low.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_answer_low.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_decline_low.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_decline_low.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_answer_video_low.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_answer_video_low.xml"}, {"merged": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/drawable-anydpi-v21/ic_call_answer_video.xml", "source": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15:/drawable-anydpi-v21/ic_call_answer_video.xml"}]