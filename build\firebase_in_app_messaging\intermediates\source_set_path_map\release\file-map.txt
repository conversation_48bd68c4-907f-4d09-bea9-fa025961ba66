io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-lifecycle-runtime-2.7.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\0656c23f9d29748157dfc921e0a1bfce\transformed\lifecycle-runtime-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-startup-runtime-1.1.1-1 C:\Users\<USER>\.gradle\caches\transforms-3\067517fbf26e28d3fc19688933378e3c\transformed\jetified-startup-runtime-1.1.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-lifecycle-livedata-2.7.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\06a8bb42ef23870d101a3cbf94012564\transformed\lifecycle-livedata-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-inappmessaging-display-21.0.1-3 C:\Users\<USER>\.gradle\caches\transforms-3\0f4a309afec50d8348c7cb8e56769eef\transformed\jetified-firebase-inappmessaging-display-21.0.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-cardview-1.0.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\191b3d8ada05bbe09a97b501b2719866\transformed\cardview-1.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-savedstate-1.2.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\21318d80f816c5a15adc2dee5ce48a9b\transformed\jetified-savedstate-1.2.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-lifecycle-viewmodel-savedstate-2.7.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\260d7b8850fd9b75b50341873c1abf51\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-play-services-basement-18.3.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\2b4f149415a6b8de84f08bfbd57a729e\transformed\jetified-play-services-basement-18.3.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-appcompat-resources-1.1.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\3cbdeaabc5fb39ae13e7527908de0453\transformed\jetified-appcompat-resources-1.1.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-lifecycle-livedata-core-ktx-2.7.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\44cca7697aa5cafc32d9560782d07580\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-core-1.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\65580ebb85f08ff018dbda1eee47f0f5\transformed\jetified-core-1.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-media-1.0.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\80593b2da926ebe4e8d028d5af975b35\transformed\media-1.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-window-1.2.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\8b695fff92e8b8d03c52c14552a09e6c\transformed\jetified-window-1.2.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-profileinstaller-1.3.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\9751eba2850da89a96c900c3d9baddab\transformed\jetified-profileinstaller-1.3.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-lifecycle-livedata-core-2.7.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\a1160652e1ea61043d00d63a24787c2d\transformed\lifecycle-livedata-core-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-1.13.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\a7c6bac0ab70d3396b83193d45a89ada\transformed\core-1.13.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-annotation-experimental-1.4.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\b9a7700117538710ee1eeaf80040f0e4\transformed\jetified-annotation-experimental-1.4.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-activity-1.8.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\bce6e27288cd6eb63e66f7f9a4a86f17\transformed\jetified-activity-1.8.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-lifecycle-viewmodel-2.7.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\c46a48074684624811ad0afd765b2e05\transformed\lifecycle-viewmodel-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-glide-4.11.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\c6ef2c27f4bc3e4a56043fbbc1696599\transformed\jetified-glide-4.11.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-core-runtime-2.2.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\c7aa0abc31651575c1616c5b4c1a7565\transformed\core-runtime-2.2.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-core-ktx-1.13.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\c827ad69d970b9d935baa07a88d9a5d9\transformed\jetified-core-ktx-1.13.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-tracing-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\c861565b3f921cfa41e8d03d3f816aac\transformed\jetified-tracing-1.2.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-browser-1.0.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\c99b580e39fd4eacdf8bb583a8794ea4\transformed\browser-1.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-firebase-common-21.0.0-24 C:\Users\<USER>\.gradle\caches\transforms-3\d12a45c3bc1f670a24f0103aa4e1261c\transformed\jetified-firebase-common-21.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-constraintlayout-1.1.3-25 C:\Users\<USER>\.gradle\caches\transforms-3\e731550fcb0230047e8f9df5991942b2\transformed\constraintlayout-1.1.3\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-fragment-1.7.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\e93a2d421962aa211f27e1b69b786d3e\transformed\fragment-1.7.1\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-lifecycle-process-2.7.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\efa7522e3aeb778d93b16a5ba1a66bd8\transformed\jetified-lifecycle-process-2.7.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-jetified-window-java-1.2.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\f0370772205c6f3cfcbe83b1a669c122\transformed\jetified-window-java-1.2.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-coordinatorlayout-1.0.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\f37499733cc5d2d902f654ef669014a9\transformed\coordinatorlayout-1.0.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-appcompat-1.1.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\fbcc4507bc3ed2a2537aed3553d037d6\transformed\appcompat-1.1.0\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-main-31 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_in_app_messaging-0.8.1+4\android\src\main\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-release-32 C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_in_app_messaging-0.8.1+4\android\src\release\res
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-packaged_res-33 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_core\intermediates\packaged_res\release
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-pngs-34 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\generated\res\pngs\release
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-resValues-35 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\generated\res\resValues\release
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-rs-36 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\generated\res\rs\release
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-37 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\intermediates\incremental\release\mergeReleaseResources\merged.dir
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-38 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\intermediates\incremental\release\mergeReleaseResources\stripped.dir
io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-39 C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\firebase_in_app_messaging\intermediates\merged_res\release
