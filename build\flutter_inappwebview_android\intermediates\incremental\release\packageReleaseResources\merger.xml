<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res"/><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\flutter_inappwebview_android\generated\res\rs\release"/><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\flutter_inappwebview_android\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res"><file name="floating_action_mode_shape" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\drawable\floating_action_mode_shape.xml" qualifiers="" type="drawable"/><file name="activity_web_view" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\layout\activity_web_view.xml" qualifiers="" type="layout"/><file name="chrome_custom_tabs_layout" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\layout\chrome_custom_tabs_layout.xml" qualifiers="" type="layout"/><file name="floating_action_mode" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\layout\floating_action_mode.xml" qualifiers="" type="layout"/><file name="floating_action_mode_item" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\layout\floating_action_mode_item.xml" qualifiers="" type="layout"/><file name="menu_main" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\values\strings.xml" qualifiers=""><string name="action_go_back">Go Back</string><string name="action_go_forward">Go Forward</string><string name="action_reload">Reload</string><string name="action_share">Share</string><string name="action_close">Close</string><string name="menu_search">Search</string></file><file path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\values\styles.xml" qualifiers=""><style name="InAppWebViewTheme" parent="Theme.AppCompat">

    </style><style name="AppTheme" parent="Theme.AppCompat.Light">

    </style><style name="ThemeTransparent" parent="android:Theme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style></file><file name="provider_paths" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\main\res\xml\provider_paths.xml" qualifiers="" type="xml"/></source><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\flutter_inappwebview_android\generated\res\rs\release"/><source path="C:\Users\<USER>\Documents\projects\Voice Bird APPS\pellipustakam\build\flutter_inappwebview_android\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_inappwebview_android-1.1.3\android\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>