<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--<ProgressBar-->
        <!--android:id="@+id/loading_indicator"-->
        <!--android:indeterminate="true"-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:layout_centerInParent="true" />-->

    <!--<LinearLayout android:id="@+id/native_ad_container"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="match_parent"-->
        <!--android:orientation="vertical"-->
        <!--android:visibility="gone">-->

        <!--<RelativeLayout-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginBottom="8dp">-->

            <!--<TextView-->
                <!--android:id="@+id/ad_attribution"-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_alignParentLeft="true"-->
                <!--android:textColor="#FFFFFF"-->
                <!--android:textSize="12sp"-->
                <!--android:text="Ad"-->
                <!--android:paddingTop="1dp"-->
                <!--android:paddingBottom="1dp"-->
                <!--android:paddingStart="3dp"-->
                <!--android:paddingEnd="3dp" />-->

            <!--<LinearLayout-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:orientation="horizontal"-->
                <!--android:layout_alignParentRight="true">-->

                <!--<RelativeLayout-->
                    <!--android:id="@+id/ad_choices_container"-->
                    <!--android:layout_width="wrap_content"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:layout_gravity="center_vertical"-->
                    <!--android:padding="2dp" />-->

                <!--<TextView-->
                    <!--android:id="@+id/native_ad_sponsored_label"-->
                    <!--android:layout_width="wrap_content"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:layout_gravity="center_vertical"-->
                    <!--android:ellipsize="end"-->
                    <!--android:lines="1"-->
                    <!--android:padding="2dp"-->
                    <!--android:textColor="@android:color/darker_gray"-->
                    <!--android:textSize="12sp" />-->
            <!--</LinearLayout>-->
        <!--</RelativeLayout>-->

        <!--<LinearLayout-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:orientation="horizontal"-->
            <!--android:layout_gravity="center_vertical"-->
            <!--android:gravity="center_vertical">-->

            <!--<com.facebook.ads.AdIconView-->
                <!--android:id="@+id/native_icon_view"-->
                <!--android:layout_width="50dp"-->
                <!--android:layout_height="50dp" />-->

            <!--<LinearLayout-->
                <!--android:layout_width="0dp"-->
                <!--android:layout_height="wrap_content"-->
                <!--android:layout_weight="1"-->
                <!--android:orientation="vertical"-->
                <!--android:paddingLeft="8dp"-->
                <!--android:paddingRight="8dp">-->

                <!--<TextView-->
                    <!--android:id="@+id/native_ad_title"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:ellipsize="end"-->
                    <!--android:lines="1"-->
                    <!--android:textSize="15sp"-->
                    <!--android:textStyle="bold" />-->

                <!--<TextView-->
                    <!--android:id="@+id/native_ad_social_context"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:ellipsize="end"-->
                    <!--android:lines="2"-->
                    <!--android:textSize="12sp" />-->
            <!--</LinearLayout>-->

            <!--<Button-->
                <!--android:id="@+id/native_ad_call_to_action"-->
                <!--android:layout_width="wrap_content"-->
                <!--android:layout_height="50dp"-->
                <!--android:background="#4286F4"-->
                <!--android:gravity="center"-->
                <!--android:textColor="@android:color/white"-->
                <!--android:textSize="12sp"-->
                <!--android:visibility="gone" />-->

        <!--</LinearLayout>-->
    <!--</LinearLayout>-->
</merge>