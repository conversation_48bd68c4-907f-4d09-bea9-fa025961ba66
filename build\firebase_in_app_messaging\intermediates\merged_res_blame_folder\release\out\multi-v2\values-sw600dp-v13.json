{"logs": [{"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-merged_res-38:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,128", "endColumns": "72,71", "endOffsets": "123,195"}, "to": {"startLines": "10,11", "startColumns": "4,4", "startOffsets": "611,684", "endColumns": "72,71", "endOffsets": "679,751"}}]}, {"outputFile": "io.flutter.plugins.firebase.inappmessaging.firebase_in_app_messaging-mergeReleaseResources-36:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fbcc4507bc3ed2a2537aed3553d037d6\\transformed\\appcompat-1.1.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f4a309afec50d8348c7cb8e56769eef\\transformed\\jetified-firebase-inappmessaging-display-21.0.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,128", "endColumns": "72,71", "endOffsets": "123,195"}, "to": {"startLines": "10,11", "startColumns": "4,4", "startOffsets": "611,684", "endColumns": "72,71", "endOffsets": "679,751"}}]}]}