[{"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_open_enter.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_open_enter.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_close_enter.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_close_enter.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_fade_exit.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_fade_exit.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_open_exit.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_open_exit.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_fade_enter.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_fade_enter.xml"}, {"merged": "com.pichillilorenzo.flutter_inappwebview_android-merged_res-33:/animator/fragment_close_exit.xml", "source": "com.pichillilorenzo.flutter_inappwebview_android-fragment-1.7.1-23:/animator/fragment_close_exit.xml"}]